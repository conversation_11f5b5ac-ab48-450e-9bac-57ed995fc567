#!/usr/bin/env python3
"""
PDF to Typst Converter for Mathematical Documents
Converts Chinese mathematical textbook PDFs to well-formatted Typst documents
"""

import fitz  # PyMuPDF
import re
import argparse
from pathlib import Path
from typing import List, Dict, Any
import json

class MathPDFConverter:
    def __init__(self, pdf_path: str):
        self.pdf_path = Path(pdf_path)
        self.doc = fitz.open(pdf_path)
        self.typst_content = []
        
    def extract_text_with_formatting(self) -> List[Dict[str, Any]]:
        """Extract text while preserving formatting information"""
        pages_content = []
        
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            blocks = page.get_text("dict")["blocks"]
            
            page_content = {
                "page": page_num + 1,
                "blocks": []
            }
            
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                page_content["blocks"].append({
                                    "text": text,
                                    "size": span["size"],
                                    "font": span["font"],
                                    "bold": "bold" in span["font"].lower(),
                                    "italic": "italic" in span["font"].lower(),
                                    "bbox": span["bbox"]
                                })
            
            pages_content.append(page_content)
        
        return pages_content
    
    def convert_to_typst(self, pages_content: List[Dict[str, Any]]) -> str:
        """Convert extracted content to Typst format"""
        typst_lines = []
        
        typst_lines.append("#set page(paper: \"a4\", margin: 2cm)")
        typst_lines.append("#set text(font: \"Noto Serif CJK SC\", lang: \"zh\")")
        typst_lines.append("#set par(justify: true, leading: 0.7em)")
        typst_lines.append("")
        
        for page in pages_content:
            typst_lines.append(f"// === Page {page['page']} ===")
            typst_lines.append("")
            
            for block in page["blocks"]:
                text = block["text"]
                
                # Skip empty lines
                if not text:
                    continue
                
                # Handle mathematical formulas
                if self.is_math_formula(text):
                    typst_lines.append(self.convert_math_formula(text))
                # Handle headings (based on size and bold)
                elif block["size"] > 14 and block["bold"]:
                    level = self.get_heading_level(block["size"])
                    typst_lines.append(f"{'=' * level} {text}")
                # Handle bold text
                elif block["bold"]:
                    typst_lines.append(f"*{text}*")
                # Handle italic text
                elif block["italic"]:
                    typst_lines.append(f"_{text}_")
                # Handle lists
                elif self.is_list_item(text):
                    typst_lines.append(f"+ {text.lstrip('•··')}")  # Chinese list markers
                # Regular paragraph
                else:
                    typst_lines.append(text)
                
                typst_lines.append("")
        
        return "\n".join(typst_lines)
    
    def is_math_formula(self, text: str) -> bool:
        """Check if text contains mathematical formula"""
        math_patterns = [
            r'[∑∏∫∂∇±≠≤≥≈∞αβγδεζηθικλμνξοπρστυφχψω]',
            r'[A-Z]\s*=\s*[0-9a-zA-Z+\-*/^()]+',
            r'f\(x\)\s*=',
            r'\d+\s*[+\-*/]\s*\d+',
            r'[a-zA-Z]_\{[a-zA-Z0-9]+\}',
            r'[a-zA-Z]\^\{[a-zA-Z0-9]+\}'
        ]
        
        return any(re.search(pattern, text) for pattern in math_patterns)
    
    def convert_math_formula(self, text: str) -> str:
        """Convert mathematical formula to Typst syntax"""
        # Replace common mathematical notation
        conversions = {
            '≤': '<=',
            '≥': '>=',
            '≠': '!=',
            '≈': '~=',
            '∞': 'infinity',
            '∑': 'sum',
            '∏': 'product',
            '∫': 'integral',
            '∂': 'partial',
            '∇': 'nabla',
            '±': 'plus.minus',
            'α': 'alpha',
            'β': 'beta', 
            'γ': 'gamma',
            'δ': 'delta',
            'ε': 'epsilon',
            'ζ': 'zeta',
            'η': 'eta',
            'θ': 'theta',
            'ι': 'iota',
            'κ': 'kappa',
            'λ': 'lambda',
            'μ': 'mu',
            'ν': 'nu',
            'ξ': 'xi',
            'ο': 'omicron',
            'π': 'pi',
            'ρ': 'rho',
            'σ': 'sigma',
            'τ': 'tau',
            'υ': 'upsilon',
            'φ': 'phi',
            'χ': 'chi',
            'ψ': 'psi',
            'ω': 'omega'
        }
        
        formula = text
        for greek, typst_greek in conversions.items():
            formula = formula.replace(greek, typst_greek)
        
        # Convert subscript/superscript notation
        formula = re.sub(r'([a-zA-Z])_\{([^}]+)\}', r'\1_\2', formula)
        formula = re.sub(r'([a-zA-Z])\^\{([^}]+)\}', r'\1^\2', formula)
        
        return f"$ {formula} $"
    
    def get_heading_level(self, font_size: float) -> int:
        """Determine heading level based on font size"""
        if font_size >= 24:
            return 1
        elif font_size >= 20:
            return 2
        elif font_size >= 16:
            return 3
        else:
            return 4
    
    def is_list_item(self, text: str) -> bool:
        """Check if text is a list item"""
        return text.startswith(('•', '·', '1.', '2.', '3.', '4.', '5.'))
    
    def save_typst(self, typst_content: str, output_path: str = None):
        """Save converted Typst content to file"""
        if not output_path:
            output_path = self.pdf_path.with_suffix('.typst')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(typst_content)
        
        print(f"Converted PDF saved to: {output_path}")
    
    def convert(self, output_path: str = None):
        """Main conversion method"""
        print(f"Converting {self.pdf_path} to Typst format...")
        
        # Extract content
        pages_content = self.extract_text_with_formatting()
        print(f"Extracted content from {len(pages_content)} pages")
        
        # Convert to Typst
        typst_content = self.convert_to_typst(pages_content)
        
        # Save result
        self.save_typst(typst_content, output_path)
        
        # Create metadata file
        self.create_metadata()
        
        print("Conversion completed successfully!")
    
    def create_metadata(self):
        """Create metadata file with conversion info"""
        metadata = {
            "source_pdf": str(self.pdf_path),
            "pages": len(self.doc),
            "conversion_date": str(Path().cwd()),
            "typst_features": ["mathematical_formulas", "chinese_text", "headings", "lists"]
        }
        
        metadata_path = self.pdf_path.with_suffix('.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

def main():
    parser = argparse.ArgumentParser(description="Convert mathematical PDF to Typst format")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("-o", "--output", help="Output Typst file path")
    
    args = parser.parse_args()
    
    converter = MathPDFConverter(args.pdf_path)
    converter.convert(args.output)

if __name__ == "__main__":
    main()