-- Database Performance Monitoring Script
-- Run this to diagnose performance issues

-- 1. Check current connection pool status
SELECT 
    'Connection Pool Status' as check_type,
    state,
    count(*) as connection_count,
    max(now() - query_start) as longest_query_duration,
    max(now() - state_change) as longest_state_duration
FROM pg_stat_activity 
WHERE datname = current_database()
GROUP BY state
ORDER BY connection_count DESC;

-- 2. Check for long-running queries
SELECT 
    'Long Running Queries' as check_type,
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state,
    wait_event_type,
    wait_event
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
  AND state != 'idle'
ORDER BY duration DESC;

-- 3. Check users table statistics
SELECT 
    'Users Table Stats' as check_type,
    schemaname,
    tablename,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows,
    n_tup_ins as total_inserts,
    n_tup_upd as total_updates,
    n_tup_del as total_deletes,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE tablename = 'users';

-- 4. Check index usage on users table
SELECT 
    'Index Usage' as check_type,
    indexrelname as index_name,
    idx_tup_read as index_reads,
    idx_tup_fetch as index_fetches,
    idx_scan as index_scans,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_tup_read = 0 THEN 'NEVER_READ'
        ELSE 'ACTIVE'
    END as status
FROM pg_stat_user_indexes 
WHERE relname = 'users'
ORDER BY idx_scan DESC;

-- 5. Check table and index sizes
SELECT 
    'Table Sizes' as check_type,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE tablename = 'users';

-- 6. Check for table bloat
SELECT 
    'Table Bloat' as check_type,
    schemaname,
    tablename,
    ROUND(
        CASE 
            WHEN pg_total_relation_size(schemaname||'.'||tablename) > 0 
            THEN (pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename))::numeric / pg_total_relation_size(schemaname||'.'||tablename) * 100
            ELSE 0 
        END, 2
    ) as bloat_percentage,
    CASE 
        WHEN pg_total_relation_size(schemaname||'.'||tablename) > 0 
        THEN 
            CASE 
                WHEN (pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename))::numeric / pg_total_relation_size(schemaname||'.'||tablename) * 100 > 20
                THEN 'HIGH_BLOAT - Consider VACUUM FULL'
                WHEN (pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename))::numeric / pg_total_relation_size(schemaname||'.'||tablename) * 100 > 10
                THEN 'MODERATE_BLOAT - Consider VACUUM'
                ELSE 'LOW_BLOAT'
            END
        ELSE 'NO_BLOAT'
    END as recommendation
FROM pg_tables 
WHERE tablename = 'users';

-- 7. Check slow query log (if enabled)
SELECT 
    'Recent Slow Queries' as check_type,
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%users%' 
  AND mean_time > 1000  -- queries taking more than 1 second on average
ORDER BY mean_time DESC
LIMIT 10;

-- 8. Check database configuration relevant to performance
SELECT 
    'DB Configuration' as check_type,
    name,
    setting,
    unit,
    context
FROM pg_settings 
WHERE name IN (
    'max_connections',
    'shared_buffers',
    'effective_cache_size',
    'work_mem',
    'maintenance_work_mem',
    'checkpoint_completion_target',
    'wal_buffers',
    'default_statistics_target',
    'random_page_cost',
    'seq_page_cost',
    'statement_timeout'
)
ORDER BY name;

-- 9. Recommendations based on current state
SELECT 
    'Performance Recommendations' as check_type,
    'Run ANALYZE public.users if last_analyze is old' as recommendation
WHERE (SELECT last_analyze FROM pg_stat_user_tables WHERE tablename = 'users') < now() - interval '1 day'

UNION ALL

SELECT 
    'Performance Recommendations' as check_type,
    'Consider VACUUM public.users if dead_tup ratio is high' as recommendation
WHERE (
    SELECT CASE 
        WHEN n_live_tup > 0 
        THEN n_dead_tup::float / n_live_tup 
        ELSE 0 
    END 
    FROM pg_stat_user_tables 
    WHERE tablename = 'users'
) > 0.1

UNION ALL

SELECT 
    'Performance Recommendations' as check_type,
    'Connection pool may be undersized - consider increasing max_connections' as recommendation
WHERE (
    SELECT count(*) 
    FROM pg_stat_activity 
    WHERE datname = current_database() AND state = 'active'
) > (
    SELECT setting::int * 0.8 
    FROM pg_settings 
    WHERE name = 'max_connections'
);
