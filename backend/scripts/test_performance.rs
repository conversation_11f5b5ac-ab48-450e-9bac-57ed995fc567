// Performance test script for the optimized user repository
use std::time::Instant;
use sqlx::PgPool;
use uuid::Uuid;
use chrono::{DateTime, Utc};

// Mock the UserRepository methods for testing
async fn test_batch_check_performance(pool: &PgPool) -> anyhow::Result<()> {
    println!("🧪 Testing batch user check performance...");
    
    // Create test data
    let test_usernames: Vec<String> = (1..=50)
        .map(|i| format!("test_user_{}", i))
        .collect();
    
    let username_refs: Vec<&String> = test_usernames.iter().collect();
    
    // Test the old approach (simulated)
    println!("📊 Testing old approach (ANY(ARRAY[...]))...");
    let start = Instant::now();
    
    // Simulate the old query pattern
    let placeholders: Vec<String> = (1..=username_refs.len()).map(|i| format!("${}", i)).collect();
    let old_query = format!(
        "SELECT username, id, created_at FROM public.users WHERE username = ANY(ARRAY[{}]) AND is_active = true",
        placeholders.join(",")
    );
    
    let mut query_builder = sqlx::query(&old_query);
    for username in &username_refs {
        query_builder = query_builder.bind(username);
    }
    
    let _old_result = query_builder.fetch_all(pool).await?;
    let old_duration = start.elapsed();
    println!("⏱️  Old approach took: {:?}", old_duration);
    
    // Test the new approach
    println!("📊 Testing new approach (VALUES with UNNEST)...");
    let start = Instant::now();
    
    // Use the new optimized query pattern
    let username_values: Vec<String> = username_refs.iter()
        .map(|u| format!("('{}')", u.replace("'", "''")))
        .collect();
    
    let new_query = format!(
        r#"
        SELECT u.username, u.id, u.created_at 
        FROM public.users u
        INNER JOIN (VALUES {}) AS input(username) ON u.username = input.username
        WHERE u.is_active = true
        "#,
        username_values.join(",")
    );
    
    let _new_result = sqlx::query(&new_query).fetch_all(pool).await?;
    let new_duration = start.elapsed();
    println!("⏱️  New approach took: {:?}", new_duration);
    
    // Calculate improvement
    if old_duration > new_duration {
        let improvement = ((old_duration.as_millis() as f64 - new_duration.as_millis() as f64) 
                          / old_duration.as_millis() as f64) * 100.0;
        println!("🚀 Performance improvement: {:.1}%", improvement);
    } else {
        println!("⚠️  New approach is slower by {:?}", new_duration - old_duration);
    }
    
    Ok(())
}

async fn test_connection_pool_health(pool: &PgPool) -> anyhow::Result<()> {
    println!("🔍 Testing connection pool health...");
    
    // Test concurrent connections
    let mut handles = Vec::new();
    
    for i in 0..10 {
        let pool_clone = pool.clone();
        let handle = tokio::spawn(async move {
            let start = Instant::now();
            let _conn = pool_clone.acquire().await;
            let duration = start.elapsed();
            println!("🔗 Connection {} acquired in {:?}", i, duration);
            duration
        });
        handles.push(handle);
    }
    
    let mut total_time = std::time::Duration::ZERO;
    for handle in handles {
        let duration = handle.await??;
        total_time += duration;
    }
    
    let avg_time = total_time / 10;
    println!("📊 Average connection acquisition time: {:?}", avg_time);
    
    if avg_time.as_secs() > 2 {
        println!("⚠️  Connection pool may be under pressure");
    } else {
        println!("✅ Connection pool is healthy");
    }
    
    Ok(())
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("🚀 Starting Deep-Mate Performance Tests");
    
    // Load environment
    dotenvy::dotenv().ok();
    
    // Connect to database
    let database_url = std::env::var("DATABASE_URL")?;
    let pool = sqlx::PgPool::connect(&database_url).await?;
    
    // Run tests
    test_connection_pool_health(&pool).await?;
    test_batch_check_performance(&pool).await?;
    
    println!("✅ Performance tests completed");
    Ok(())
}
