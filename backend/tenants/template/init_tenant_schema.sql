CREATE TABLE IF NOT EXISTS {schema}.subject_groups
(
    id             uuid                     default gen_random_uuid() not null
    primary key,
    group_name     varchar(100)                                       not null,
    subject_code   varchar(50)                                        not null,
    description    text,
    leader_user_id uuid,
    created_at     timestamp with time zone default now(),
    updated_at     timestamp with time zone default now(),
    is_active      boolean                  default true              not null
    );

alter table {schema}.subject_groups
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_subject_groups_subject
    on {schema}.subject_groups (subject_code);

CREATE INDEX IF NOT EXISTS idx_{schema}_subject_groups_leader
    on {schema}.subject_groups (leader_user_id);

-- 学科组成员表
CREATE TABLE IF NOT EXISTS {schema}.subject_group_members
(
    id                uuid                     default gen_random_uuid() not null
        primary key,
    subject_group_id  uuid                                               not null,
    teacher_id        uuid                                               not null,
    role_code         VARCHAR(100),
    joined_at         timestamp with time zone default now()            not null,
    is_active         boolean                  default true              not null,
    created_at        timestamp with time zone default now()            not null,
    updated_at        timestamp with time zone default now()            not null,

    -- 唯一约束：同一教师在同一学科组中只能有一个活跃记录
    constraint uk_subject_group_members_active
        unique (subject_group_id, teacher_id, is_active) deferrable initially deferred
);

alter table {schema}.subject_group_members
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_subject_group_members_subject_group_id
    on {schema}.subject_group_members (subject_group_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_subject_group_members_teacher_id
    on {schema}.subject_group_members (teacher_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_subject_group_members_is_active
    on {schema}.subject_group_members (is_active);

CREATE TABLE IF NOT EXISTS {schema}.user_identities
(
    id          uuid                     default gen_random_uuid() not null    primary key,
    user_id     uuid                                               not null    references public.users,
    role_id     uuid                                               not null    references public.roles,
    target_type varchar(30),
    target_id   uuid,
    subject     varchar(50),
    created_at  timestamp with time zone default now(),
    updated_at  timestamp with time zone default now(),
    unique (user_id, role_id, target_id, subject)
);

alter table {schema}.user_identities
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_user
    on {schema}.user_identities (user_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_role
    on {schema}.user_identities (role_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_target
    on {schema}.user_identities (target_type, target_id);

CREATE TABLE IF NOT EXISTS {schema}.students
(
    id                      uuid                     default gen_random_uuid() not null
    primary key,
    student_number          varchar(50)                                        not null
    constraint students_student_id_key
    unique,
    student_name                    varchar(100)                                       not null,
    gender                  varchar(10),
    birth_date              date,
    id_number               varchar(20),
    phone                   varchar(20),
    email                   varchar(100),
    address                 text,
    guardian_name           varchar(100),
    guardian_phone          varchar(20),
    guardian_relation       varchar(20),
    administrative_class_id uuid,
    grade_level_id          uuid
    references public.grade_levels,
    user_id                 uuid
    references public.users,
    enrollment_date         date,
    status                  varchar(20)              default 'active'::character varying
    constraint students_status_check
    check ((status)::text = ANY
((ARRAY ['active'::character varying, 'inactive'::character varying, 'graduated'::character varying, 'transferred'::character varying])::text[])),
    profile_level           varchar(20)
    constraint students_profile_level_check
    check ((profile_level)::text = ANY
((ARRAY ['A+'::character varying, 'A'::character varying, 'B+'::character varying, 'B'::character varying, 'C+'::character varying, 'C'::character varying, 'D+'::character varying, 'D'::character varying])::text[])),
    profile_tags            jsonb,
    notes                   text,
    created_at              timestamp with time zone default now(),
    updated_at              timestamp with time zone default now()
    );

alter table {schema}.students
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_students_class
    on {schema}.students (administrative_class_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_students_grade
    on {schema}.students (grade_level_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_students_user
    on {schema}.students (user_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_students_student_id
    on {schema}.students (student_number);

CREATE INDEX IF NOT EXISTS idx_{schema}_students_status
    on {schema}.students (status);

CREATE TABLE IF NOT EXISTS {schema}.student_profile_levels
(
    id                uuid                     default gen_random_uuid() not null
    primary key,
    student_id        uuid
    references {schema}.students
    on delete cascade,
    subject           varchar(50)                                        not null,
    level             varchar(20)
    constraint student_profile_levels_level_check
    check ((level)::text = ANY
((ARRAY ['A+'::character varying, 'A'::character varying, 'B+'::character varying, 'B'::character varying, 'C+'::character varying, 'C'::character varying, 'D+'::character varying, 'D'::character varying])::text[])),
    level_description text,
    assessment_date   date                                               not null,
    assessed_by       uuid
    references public.users,
    created_at        timestamp with time zone default now(),
    updated_at        timestamp with time zone default now(),
    unique (student_id, subject, assessment_date)
    );

alter table {schema}.student_profile_levels
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_student_profile_levels_student
    on {schema}.student_profile_levels (student_id);

CREATE TABLE IF NOT EXISTS {schema}.student_profile_tags
(
    id           uuid                     default gen_random_uuid() not null
    primary key,
    student_id   uuid
    references {schema}.students
    on delete cascade,
    tag_name     varchar(50)                                        not null,
    tag_value    varchar(100),
    tag_category varchar(30)
    constraint student_profile_tags_tag_category_check
    check ((tag_category)::text = ANY
((ARRAY ['academic'::character varying, 'behavior'::character varying, 'interest'::character varying, 'ability'::character varying, 'other'::character varying])::text[])),
    created_by   uuid
    references public.users,
    created_at   timestamp with time zone default now(),
    updated_at   timestamp with time zone default now(),
    unique (student_id, tag_name)
    );

alter table {schema}.student_profile_tags
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_student_profile_tags_student
    on {schema}.student_profile_tags (student_id);

CREATE TABLE IF NOT EXISTS {schema}.teachers
(
    id                uuid                     default gen_random_uuid()         not null
    primary key,
    user_id           uuid,
    employee_id       varchar(50)                                                not null,
    teacher_name              varchar(100)                                               not null,
    phone             varchar(20)
    constraint valid_phone
    check ((phone IS NULL) OR ((phone)::text ~ '^[0-9+\-\s()]+$'::text)),
    email             varchar(100)
    constraint valid_email
    check ((email IS NULL) OR ((email)::text ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'::text)),
    gender            varchar(10)
    constraint teachers_gender_check
    check ((gender)::text = ANY
((ARRAY ['男'::character varying, '女'::character varying, '未知'::character varying])::text[])),
    date_of_birth     timestamp with time zone,
        id_card_number    varchar(20)
    constraint valid_id_card
    check ((id_card_number IS NULL) OR (length((id_card_number)::text) = ANY (ARRAY [15, 18]))),
    highest_education varchar(50),
    graduation_school varchar(100),
    major             varchar(100),
    hire_date         date,
        employment_status varchar(20)              default '在职'::character varying not null
    constraint teachers_employment_status_check
    check ((employment_status)::text = ANY
((ARRAY ['在职'::character varying, '离职'::character varying, '退休'::character varying, '停职'::character varying, '试用期'::character varying])::text[])),
    title             varchar(50),
    teaching_subjects text[],
    homeroom_class_id bigint,
    grade_level_id    integer,
    subject_group_id  bigint,
    office_location   varchar(100),
    bio               text,
    is_active         boolean                  default true                      not null,
    created_at        timestamp with time zone default now(),
    updated_at        timestamp with time zone default now()
    );

alter table {schema}.teachers
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_user_id
    on {schema}.teachers (user_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_employee_id
    on {schema}.teachers (employee_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_name
    on {schema}.teachers (teacher_name);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_employment_status
    on {schema}.teachers (employment_status);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_is_active
    on {schema}.teachers (is_active);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_teaching_subjects
    on {schema}.teachers using gin (teaching_subjects);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_homeroom_class_id
    on {schema}.teachers (homeroom_class_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_grade_level_id
    on {schema}.teachers (grade_level_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_subject_group_id
    on {schema}.teachers (subject_group_id);

CREATE TABLE IF NOT EXISTS {schema}.exams
(
    id                       uuid                     default gen_random_uuid() not null
    primary key,
    name                     varchar(100)                                       not null,
    type                     varchar(20)
    constraint exams_type_check
    check ((type)::text = ANY ((ARRAY ['single'::character varying, 'joint'::character varying])::text[])),
    grade_level              varchar(50)                                        not null,
    exam_nature              varchar(20)
    constraint exams_exam_nature_check
    check ((exam_nature)::text = ANY
((ARRAY ['formal'::character varying, 'mock'::character varying, 'practice'::character varying])::text[])),
    description              text,
    start_time               timestamp with time zone                           not null,
    end_time                 timestamp with time zone                           not null,
    expected_collection_time timestamp with time zone,
    scan_start_time          timestamp with time zone,
                                           grading_mode             varchar(20)
    constraint exams_grading_mode_check
    check ((grading_mode)::text = ANY
((ARRAY ['intelligent'::character varying, 'manual_then_scan'::character varying])::text[])),
    quality_control          varchar(20)
    constraint exams_quality_control_check
    check ((quality_control)::text = ANY
((ARRAY ['single'::character varying, 'double_blind'::character varying])::text[])),
    ai_confidence_threshold  numeric(3, 2),
    manual_review_ratio      numeric(3, 2),
    status                   varchar(20)              default 'draft'::character varying
    constraint exams_status_check
    check ((status)::text = ANY
((ARRAY ['draft'::character varying, 'published'::character varying, 'in_progress'::character varying, 'completed'::character varying])::text[])),
    created_by               uuid
    references public.users,
    created_at               timestamp with time zone default now(),
    updated_at               timestamp with time zone default now()
    );

alter table {schema}.exams
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_exams_type
    on {schema}.exams (type);

CREATE INDEX IF NOT EXISTS idx_{schema}_exams_status
    on {schema}.exams (status);

CREATE INDEX IF NOT EXISTS idx_{schema}_exams_date
    on {schema}.exams (start_time);

CREATE INDEX IF NOT EXISTS idx_{schema}_exams_creator
    on {schema}.exams (created_by);

CREATE TABLE IF NOT EXISTS {schema}.exam_subjects
(
    id                uuid                     default gen_random_uuid() not null
    primary key,
    exam_id           uuid
    references {schema}.exams
    on delete cascade,
    subject_id        uuid
    references public.subjects,
    paper_template_id uuid
    references public.exam_papers,
    total_score       numeric(5, 2),
    pass_score        numeric(5, 2),
    created_at        timestamp with time zone default now()
    );

alter table {schema}.exam_subjects
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_exam_subjects_exam
    on {schema}.exam_subjects (exam_id);

CREATE TABLE IF NOT EXISTS {schema}.exam_students
(
    id            uuid                     default gen_random_uuid() not null
    primary key,
    exam_id       uuid
    references {schema}.exams
    on delete cascade,
    student_id    uuid
    references {schema}.students
    on delete cascade,
    class_id      uuid,
    seat_number   varchar(20),
    is_absent     boolean                  default false,
    absent_reason text,
    created_at    timestamp with time zone default now()
    );

alter table {schema}.exam_students
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_exam_students_exam
    on {schema}.exam_students (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_exam_students_student
    on {schema}.exam_students (student_id);

CREATE TABLE IF NOT EXISTS {schema}.joint_exams
(
    id                    uuid                     default gen_random_uuid() not null
    primary key,
    main_exam_id          uuid
    references {schema}.exams
    on delete cascade,
    organizer_tenant_id   uuid
    references public.tenants,
    participant_tenant_id uuid
    references public.tenants,
    invitation_status     varchar(20)
    constraint joint_exams_invitation_status_check
    check ((invitation_status)::text = ANY
((ARRAY ['pending'::character varying, 'accepted'::character varying, 'rejected'::character varying])::text[])),
    invitation_sent_at    timestamp with time zone default now(),
    responded_at          timestamp with time zone,
        sync_status           varchar(20)
    constraint joint_exams_sync_status_check
    check ((sync_status)::text = ANY
((ARRAY ['pending'::character varying, 'synced'::character varying, 'failed'::character varying])::text[])),
    created_at            timestamp with time zone default now()
    );

alter table {schema}.joint_exams
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.answer_card_blocks
(
    id               uuid                     default gen_random_uuid() not null
    primary key,
    exam_id          uuid
    references {schema}.exams
    on delete cascade,
    block_name       varchar(100)                                       not null,
    block_type       varchar(30)
    constraint answer_card_blocks_block_type_check
    check ((block_type)::text = ANY
((ARRAY ['single_question'::character varying, 'multi_question'::character varying, 'composite'::character varying])::text[])),
    position_info    jsonb                                              not null,
    template_version varchar(50),
    max_score        numeric(5, 2),
    is_active        boolean                  default true,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now()
    );

alter table {schema}.answer_card_blocks
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.card_block_question_links
(
    id            uuid                     default gen_random_uuid() not null
    primary key,
    card_block_id uuid
    references {schema}.answer_card_blocks
    on delete cascade,
    question_id   uuid                                               not null,
    link_type     varchar(20)
    constraint card_block_question_links_link_type_check
    check ((link_type)::text = ANY
((ARRAY ['one_to_one'::character varying, 'one_to_many'::character varying, 'many_to_one'::character varying, 'many_to_many'::character varying])::text[])),
    weight_ratio  numeric(3, 2)            default 1.0,
    score_mapping jsonb,
    is_primary    boolean                  default true,
    created_at    timestamp with time zone default now(),
    updated_at    timestamp with time zone default now(),
    unique (card_block_id, question_id)
    );

alter table {schema}.card_block_question_links
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.grading_assignments
(
    id                uuid                     default gen_random_uuid() not null
    primary key,
    exam_id           uuid
    references {schema}.exams
    on delete cascade,
    question_id       uuid                                               not null,
    student_id        uuid
    references {schema}.students
    on delete cascade,
    grader_user_id    uuid
    references public.users,
    assignment_type   varchar(20)
    constraint grading_assignments_assignment_type_check
    check ((assignment_type)::text = ANY
((ARRAY ['manual'::character varying, 'ai'::character varying, 'hybrid'::character varying])::text[])),
    assignment_method varchar(20)
    constraint grading_assignments_assignment_method_check
    check ((assignment_method)::text = ANY
((ARRAY ['by_quantity'::character varying, 'by_difficulty'::character varying, 'by_subject'::character varying, 'random'::character varying])::text[])),
    priority_level    integer                  default 3
    constraint grading_assignments_priority_level_check
    check ((priority_level >= 1) AND (priority_level <= 5)),
    assigned_at       timestamp with time zone default now(),
    started_at        timestamp with time zone,
    completed_at      timestamp with time zone,
        status            varchar(20)              default 'assigned'::character varying
    constraint grading_assignments_status_check
    check ((status)::text = ANY
((ARRAY ['assigned'::character varying, 'in_progress'::character varying, 'completed'::character varying, 'paused'::character varying, 'cancelled'::character varying])::text[])),
    estimated_time    integer,
    actual_time       integer,
    created_at        timestamp with time zone default now(),
    updated_at        timestamp with time zone default now()
    );

alter table {schema}.grading_assignments
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_assignments_exam
    on {schema}.grading_assignments (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_assignments_grader
    on {schema}.grading_assignments (grader_user_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_assignments_status
    on {schema}.grading_assignments (status);

CREATE TABLE IF NOT EXISTS {schema}.grading_records
(
    id             uuid                     default gen_random_uuid() not null
    primary key,
    exam_id        uuid
    references {schema}.exams
    on delete cascade,
    student_id     uuid
    references {schema}.students
    on delete cascade,
    question_id    uuid                                               not null,
    grader_user_id uuid
    references public.users,
    original_score numeric(5, 2),
    final_score    numeric(5, 2),
    grading_method varchar(20)
    constraint grading_records_grading_method_check
    check ((grading_method)::text = ANY
((ARRAY ['manual'::character varying, 'ai'::character varying, 'hybrid'::character varying])::text[])),
    grading_time   integer,
    grading_notes  text,
    is_reviewed    boolean                  default false,
    reviewed_by    uuid
    references public.users,
    reviewed_at    timestamp with time zone,
    created_at     timestamp with time zone default now(),
    updated_at     timestamp with time zone default now()
    );

alter table {schema}.grading_records
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_exam
    on {schema}.grading_records (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_student
    on {schema}.grading_records (student_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_grader
    on {schema}.grading_records (grader_user_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_question
    on {schema}.grading_records (question_id);

CREATE TABLE IF NOT EXISTS {schema}.card_block_grading_records
(
    id               uuid                     default gen_random_uuid() not null
    primary key,
    exam_id          uuid
    references {schema}.exams
    on delete cascade,
    student_id       uuid
    references {schema}.students
    on delete cascade,
    card_block_id    uuid
    references {schema}.answer_card_blocks
    on delete cascade,
    grader_user_id   uuid
    references public.users,
    raw_score        numeric(5, 2)                                      not null,
    adjusted_score   numeric(5, 2),
    grading_method   varchar(20)
    constraint card_block_grading_records_grading_method_check
    check ((grading_method)::text = ANY
((ARRAY ['manual'::character varying, 'ai'::character varying, 'hybrid'::character varying])::text[])),
    confidence_score numeric(3, 2),
    grading_notes    text,
    quality_level    varchar(20)
    constraint card_block_grading_records_quality_level_check
    check ((quality_level)::text = ANY
((ARRAY ['excellent'::character varying, 'good'::character varying, 'fair'::character varying, 'poor'::character varying])::text[])),
    is_reviewed      boolean                  default false,
    reviewed_by      uuid
    references public.users,
    reviewed_at      timestamp with time zone,
    grading_duration integer,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now()
    );

alter table {schema}.card_block_grading_records
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_card_block_grading_records_exam
    on {schema}.card_block_grading_records (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_card_block_grading_records_student
    on {schema}.card_block_grading_records (student_id);

CREATE TABLE IF NOT EXISTS {schema}.ai_grading_records
(
    id                  uuid                     default gen_random_uuid() not null
    primary key,
    exam_id             uuid
    references {schema}.exams
    on delete cascade,
    student_id          uuid
    references {schema}.students
    on delete cascade,
    question_id         uuid                                               not null,
    ai_agent_id         varchar(100)                                       not null,
    ai_model_version    varchar(50)                                        not null,
    ai_model_result     jsonb                                              not null,
    confidence_score    numeric(3, 2),
    processing_time     integer,
    error_message       text,
    reviewed_by_human   boolean                  default false,
    human_reviewer_id   uuid
    references public.users,
    human_review_result jsonb,
    created_at          timestamp with time zone default now()
    );

alter table {schema}.ai_grading_records
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_ai_grading_records_exam
    on {schema}.ai_grading_records (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_ai_grading_records_student
    on {schema}.ai_grading_records (student_id);

CREATE TABLE IF NOT EXISTS {schema}.grading_statistics
(
    id                 uuid                     default gen_random_uuid() not null
    primary key,
    exam_id            uuid
    references {schema}.exams
    on delete cascade,
    question_id        uuid                                               not null,
    question_type      varchar(20)                                        not null,
    grader_user_id     uuid
    references public.users,
    total_papers       integer                  default 0,
    avg_score          numeric(5, 2),
    min_score          numeric(5, 2),
    max_score          numeric(5, 2),
    score_distribution jsonb,
    consistency_score  numeric(3, 2),
    created_at         timestamp with time zone default now(),
    updated_at         timestamp with time zone default now()
    );

alter table {schema}.grading_statistics
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.grading_control_states
(
    id             uuid                     default gen_random_uuid() not null
    primary key,
    exam_id        uuid
    references {schema}.exams
    on delete cascade,
    question_id    uuid,
    grader_user_id uuid
    references public.users,
    control_level  varchar(20)
    constraint grading_control_states_control_level_check
    check ((control_level)::text = ANY
((ARRAY ['global'::character varying, 'public_resource'::character varying, 'grader'::character varying])::text[])),
    control_action varchar(20)
    constraint grading_control_states_control_action_check
    check ((control_action)::text = ANY
((ARRAY ['start'::character varying, 'pause'::character varying, 'resume'::character varying, 'stop'::character varying])::text[])),
    control_reason text,
    controlled_by  uuid
    references public.users,
    is_active      boolean                  default true,
    created_at     timestamp with time zone default now(),
    updated_at     timestamp with time zone default now()
    );

alter table {schema}.grading_control_states
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.academic_statistics
(
    id                uuid                     default gen_random_uuid() not null
    primary key,
    exam_id           uuid
    references {schema}.exams
    on delete cascade,
    student_id        uuid
    references {schema}.students
    on delete cascade,
    subject           varchar(50)                                        not null,
    total_score       numeric(5, 2),
    class_rank        integer,
    grade_rank        integer,
    school_rank       integer,
    is_absent         boolean                  default false,
    absent_reason     text,
    performance_trend jsonb,
    created_at        timestamp with time zone default now()
    );

alter table {schema}.academic_statistics
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_academic_stats_exam
    on {schema}.academic_statistics (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_academic_stats_student
    on {schema}.academic_statistics (student_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_academic_stats_subject
    on {schema}.academic_statistics (subject);

CREATE TABLE IF NOT EXISTS {schema}.question_analysis
(
    id                       uuid                     default gen_random_uuid() not null
    primary key,
    exam_id                  uuid
    references {schema}.exams
    on delete cascade,
    question_id              uuid                                               not null,
    question_type            varchar(20)                                        not null,
    total_students           integer,
    correct_count            integer,
    score_rate               numeric(5, 2),
    average_score            numeric(5, 2),
    score_distribution       jsonb,
    option_distribution      jsonb,
    zero_score_count         integer,
    full_score_count         integer,
    difficulty_coefficient   numeric(3, 2),
    discrimination_index     numeric(3, 2),
    knowledge_points_mastery jsonb,
    created_at               timestamp with time zone default now()
    );

alter table {schema}.question_analysis
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_question_analysis_exam
    on {schema}.question_analysis (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_question_analysis_question
    on {schema}.question_analysis (question_id);

CREATE TABLE IF NOT EXISTS {schema}.question_scores
(
    id               uuid                     default gen_random_uuid() not null
    primary key,
    exam_id          uuid
    references {schema}.exams
    on delete cascade,
    student_id       uuid
    references {schema}.students
    on delete cascade,
    question_id      uuid                                               not null,
    question_type    varchar(20)                                        not null,
    max_score        numeric(5, 2)                                      not null,
    actual_score     numeric(5, 2)                                      not null,
    score_percentage numeric(5, 2) generated always as (((actual_score / max_score) * (100)::numeric)) stored,
    answer_content   text,
    is_correct       boolean,
    difficulty_level integer
    constraint question_scores_difficulty_level_check
    check ((difficulty_level >= 1) AND (difficulty_level <= 5)),
    knowledge_points jsonb,
    created_at       timestamp with time zone default now()
    );

alter table {schema}.question_scores
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_question_scores_exam
    on {schema}.question_scores (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_question_scores_student
    on {schema}.question_scores (student_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_question_scores_question
    on {schema}.question_scores (question_id);

CREATE TABLE IF NOT EXISTS {schema}.learning_records
(
    id                    uuid                     default gen_random_uuid() not null
    primary key,
    student_id            uuid                                               not null
    references {schema}.students,
    question_id           uuid                                               not null,
    grading_record_id     uuid
    references {schema}.card_block_grading_records,
    exam_id               uuid
    references {schema}.exams,
    subject               varchar(50)                                        not null,
    knowledge_points      jsonb,
    difficulty_level      integer
    constraint learning_records_difficulty_level_check
    check ((difficulty_level >= 1) AND (difficulty_level <= 5)),
    student_score         numeric(5, 2)                                      not null,
    max_score             numeric(5, 2)                                      not null,
    score_rate            numeric(5, 2) generated always as (((student_score / max_score) * (100)::numeric)) stored,
    mastery_level         varchar(20)
    constraint learning_records_mastery_level_check
    check ((mastery_level)::text = ANY
((ARRAY ['excellent'::character varying, 'good'::character varying, 'fair'::character varying, 'poor'::character varying, 'not_mastered'::character varying])::text[])),
    learning_suggestions  jsonb,
    recommended_exercises jsonb,
    improvement_areas     jsonb,
    historical_comparison jsonb,
    generated_at          timestamp with time zone default now(),
    updated_at            timestamp with time zone default now(),
    unique (student_id, question_id, exam_id)
    );

alter table {schema}.learning_records
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_learning_records_student
    on {schema}.learning_records (student_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_learning_records_question
    on {schema}.learning_records (question_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_learning_records_exam
    on {schema}.learning_records (exam_id);

CREATE TABLE IF NOT EXISTS {schema}.learning_record_versions
(
    id                 uuid                     default gen_random_uuid() not null
    primary key,
    learning_record_id uuid
    references {schema}.learning_records
    on delete cascade,
    version_number     integer                                            not null,
    change_reason      varchar(100),
    changed_fields     jsonb,
    previous_data      jsonb,
    changed_by         uuid
    references public.users,
    created_at         timestamp with time zone default now()
    );

alter table {schema}.learning_record_versions
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.student_id_exceptions
(
    id                   uuid                     default gen_random_uuid() not null
    primary key,
    scan_id              uuid,
    detected_student_id  varchar(50),
    exception_type       varchar(30)
    constraint student_id_exceptions_exception_type_check
    check ((exception_type)::text = ANY
((ARRAY ['unrecognized'::character varying, 'blurred'::character varying, 'missing'::character varying, 'invalid'::character varying, 'duplicate'::character varying])::text[])),
    suggested_students   jsonb,
    confirmed_student_id uuid
    references {schema}.students,
    confirmed_by         uuid
    references public.users,
    confirmed_at         timestamp with time zone,
                                       resolution_method    varchar(20)
    constraint student_id_exceptions_resolution_method_check
    check ((resolution_method)::text = ANY
((ARRAY ['auto_match'::character varying, 'manual_input'::character varying, 'name_match'::character varying, 'teacher_confirm'::character varying])::text[])),
    processing_notes     text,
    created_at           timestamp with time zone default now(),
    updated_at           timestamp with time zone default now()
    );

alter table {schema}.student_id_exceptions
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_student_id_exceptions_scan
    on {schema}.student_id_exceptions (scan_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_student_id_exceptions_type
    on {schema}.student_id_exceptions (exception_type);

CREATE TABLE IF NOT EXISTS {schema}.paper_scan_exceptions
(
    id                    uuid                     default gen_random_uuid() not null
    primary key,
    scan_id               uuid,
    exception_type        varchar(30)
    constraint paper_scan_exceptions_exception_type_check
    check ((exception_type)::text = ANY
((ARRAY ['duplicate'::character varying, 'blank'::character varying, 'blurred'::character varying, 'damaged'::character varying, 'orientation'::character varying, 'other'::character varying])::text[])),
    exception_description text,
    auto_detected         boolean                  default true,
    confidence_score      numeric(3, 2),
    resolution_status     varchar(20)              default 'pending'::character varying
    constraint paper_scan_exceptions_resolution_status_check
    check ((resolution_status)::text = ANY
((ARRAY ['pending'::character varying, 'resolved'::character varying, 'ignored'::character varying])::text[])),
    resolved_by           uuid
    references public.users,
    resolved_at           timestamp with time zone,
    resolution_notes      text,
    created_at            timestamp with time zone default now()
    );

alter table {schema}.paper_scan_exceptions
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_exceptions_scan
    on {schema}.paper_scan_exceptions (scan_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_exceptions_status
    on {schema}.paper_scan_exceptions (resolution_status);

CREATE TABLE IF NOT EXISTS {schema}.homework
(
    id               uuid                     default gen_random_uuid()          not null
    primary key,
    homework_name    varchar(128)                                                not null,
    homework_status  varchar(64)              default 'Draft'::character varying not null,
    description      varchar(1024),
    leaf_count int4 DEFAULT 0    not null,
    leaf_total int4 DEFAULT 0    not null,
    page_count int4 DEFAULT 0    not null,
    page_total int4 DEFAULT 0    not null,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    subject_group_id uuid
    );

comment on table {schema}.homework is '作业表';

alter table {schema}.homework
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.homework_papers (
     id uuid NOT NULL DEFAULT gen_random_uuid(),
     homework_id uuid NOT NULL,
     paper_id uuid NOT NULL,
     CONSTRAINT homework_papers_pk PRIMARY KEY (id)
);

ALTER TABLE {schema}.homework_papers
    OWNER TO "deep_mate";

CREATE TABLE IF NOT EXISTS {schema}.homework_feedback (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    homework_id uuid NOT NULL,
    student_id uuid NOT NULL,
    score_id uuid,
    text text NOT NULL,
    status varchar(20) NOT NULL
);

ALTER TABLE {schema}.homework_feedback
    OWNER TO "deep_mate";

CREATE TABLE {schema}.papers (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "paper_name" varchar(250) COLLATE "pg_catalog"."default" NOT NULL,
    "paper_content" jsonb,
    "created_at" timestamptz(6) DEFAULT now(),
    "updated_at" timestamptz(6) DEFAULT now(),
    CONSTRAINT "papers_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS {schema}.homework_subjects
(
    id                uuid                     default gen_random_uuid() not null
    primary key,
    homework_id       uuid
    references {schema}.homework
    on delete cascade,
    subject_id        uuid
    references public.subjects,
    paper_template_id uuid
    references public.exam_papers,
    total_score       numeric(5, 2),
    created_at        timestamp with time zone default now()
    );

alter table {schema}.homework_subjects
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_homework_subjects_homework
    on {schema}.homework_subjects (homework_id);

CREATE TABLE IF NOT EXISTS {schema}.homework_students
(
    id          uuid                     default gen_random_uuid() not null
    primary key,
    homework_id uuid
    references {schema}.homework
    on delete cascade,
    student_id  uuid
    references {schema}.students
    on delete cascade,
    "score" numeric(10,2),
    created_at  timestamp with time zone default now(),
    status      varchar                  default 'Unsubmitted'::character varying,
    "class_id" uuid NOT NULL,
    updated_at  timestamp with time zone default now()
    );

alter table {schema}.homework_students
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_homework_students_homework
    on {schema}.homework_students (homework_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_homework_students_student
    on {schema}.homework_students (student_id);

create unique index homework_students_homework_id_idx
    on {schema}.homework_students (homework_id, student_id);

CREATE TABLE IF NOT EXISTS {schema}.administrative_classes
(
    id               uuid                     default gen_random_uuid() not null
    constraint administrative_classes_pk
    primary key,
    class_name       varchar(100)                                       not null,
    code             varchar(50)
    constraint administrative_classes_unique
    unique,
    academic_year    varchar(20),
    grade_level_code varchar(20),
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    teacher_id       uuid,
    is_active        boolean                  default true
    );

comment on table {schema}.administrative_classes is '行政班级表';

alter table {schema}.administrative_classes
    owner to deep_mate;

CREATE TABLE IF NOT EXISTS {schema}.teaching_classes
(
    id               uuid                     default gen_random_uuid() not null
    primary key,
    class_name       varchar(100)                                       not null,
    code             varchar(50),
    academic_year    varchar(20),
    subject_group_id uuid,
    teacher_id       uuid,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    is_active        boolean                  default true
    );

alter table {schema}.teaching_classes
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_teaching_classes_code
    on {schema}.teaching_classes (code);

CREATE INDEX IF NOT EXISTS idx_{schema}_teaching_classes_subject_group
    on {schema}.teaching_classes (subject_group_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_teaching_classes_teacher
    on {schema}.teaching_classes (teacher_id);

CREATE TABLE IF NOT EXISTS {schema}.student_teaching_classes
(
    id         uuid                     default gen_random_uuid() not null
    constraint student_teaching_classes_pkey_1
    primary key,
    student_id uuid,
    class_id   uuid,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now(),
    unique (student_id, class_id)
    );

alter table {schema}.student_teaching_classes
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_student_teaching_classes_student
    on {schema}.student_teaching_classes (student_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_student_teaching_classes_teaching_clas
    on {schema}.student_teaching_classes (class_id);

CREATE TABLE IF NOT EXISTS {schema}.answer_block_scoring_criteria
(
    id            uuid    not null
    constraint answer_block_scoring_criteria_unique
    unique,
    block_id      uuid    not null,
    serial_number integer,
    scoing_type   varchar not null,
    config        jsonb,
    is_enable     boolean default false,
    answer        varchar,
    score         integer default 0
);

comment on table {schema}.answer_block_scoring_criteria is '块关联评分标准';

comment on column {schema}.answer_block_scoring_criteria.id is '主键';

comment on column {schema}.answer_block_scoring_criteria.block_id is '题块ID';

comment on column {schema}.answer_block_scoring_criteria.serial_number is '序号';

comment on column {schema}.answer_block_scoring_criteria.scoing_type is '评价类型';

comment on column {schema}.answer_block_scoring_criteria.config is '对应配置评分细则';

comment on column {schema}.answer_block_scoring_criteria.is_enable is '是否启用,只能启用一个';

comment on column {schema}.answer_block_scoring_criteria.answer is '作答内容';

comment on column {schema}.answer_block_scoring_criteria.score is '分值';

alter table {schema}.answer_block_scoring_criteria
    owner to deep_mate;

CREATE TABLE {schema}."scores" (
                                            "id" uuid NOT NULL,
                                            "criteria_id" uuid NOT NULL,
                                            "student_id" uuid,
                                            "score_status" varchar COLLATE "pg_catalog"."default",
                                            "score" numeric(10,2),
                                            CONSTRAINT "score_unique" UNIQUE ("id")
);

ALTER TABLE {schema}."scores"
    OWNER TO "deep_mate";

COMMENT ON COLUMN {schema}."scores"."id" IS '主键';

COMMENT ON COLUMN {schema}."scores"."criteria_id" IS '评分块ID';

COMMENT ON COLUMN {schema}."scores"."student_id" IS '学生唯一ID';

COMMENT ON COLUMN {schema}."scores"."score_status" IS '评分状态(未分发、已分发OCR、OCR识别失败、)';

COMMENT ON COLUMN {schema}."scores"."score" IS '分数';

COMMENT ON TABLE {schema}."scores" IS '块关联评分结果';

CREATE TABLE {schema}."score_details" (
                                                   "id" uuid NOT NULL,
                                                   "score_id" uuid,
                                                   "reason" jsonb,
                                                   "score" numeric(10,2) DEFAULT 0,
                                                   "scoring_type" jsonb,
                                                   "created_at" timestamptz(6) NOT NULL DEFAULT now(),
                                                   "ocr" text COLLATE "pg_catalog"."default",
                                                   "status" varchar(20) COLLATE "pg_catalog"."default"
)
;

ALTER TABLE {schema}."score_details"
    OWNER TO "deep_mate";

COMMENT ON COLUMN {schema}."score_details"."id" IS '主键ID';

COMMENT ON COLUMN {schema}."score_details"."score_id" IS '评分主表ID';

COMMENT ON COLUMN {schema}."score_details"."reason" IS '评分理由';

COMMENT ON COLUMN {schema}."score_details"."score" IS '分数';

COMMENT ON COLUMN {schema}."score_details"."scoring_type" IS '评分类型(AI评阅、人工打分)';

COMMENT ON COLUMN {schema}."score_details"."ocr" IS 'ocr结果';

COMMENT ON COLUMN {schema}."score_details"."status" IS '状态（评分错误/成功等）';

COMMENT ON TABLE {schema}."score_details" IS '评分理由';
CREATE TABLE {schema}."score_blocks" (
                                                  "score_id" uuid NOT NULL,
                                                  "block_id" uuid NOT NULL,
                                                  CONSTRAINT "score_block_unique" UNIQUE ("score_id", "block_id")
);

COMMENT ON CONSTRAINT "score_block_unique" ON {schema}."score_blocks" IS '分数切块关联主键';
CREATE TABLE IF NOT EXISTS {schema}.paper_scans
(
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "exam_id" uuid,
    "student_id" uuid,
    "student_number" varchar(20) COLLATE "pg_catalog"."default",
    "scan_method" varchar(20) COLLATE "pg_catalog"."default",
    "scan_device" varchar(100) COLLATE "pg_catalog"."default",
    "status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'unprocessed'::character varying,
    "result" jsonb,
    "duplicate_num" int4 DEFAULT 0,
    "blank_num" int4 DEFAULT 0,
    "is_abnormal" bool DEFAULT false,
    "abnormal_reason" text COLLATE "pg_catalog"."default",
    "needs_review" bool DEFAULT false,
    "reviewed_by" uuid,
    "reviewed_at" timestamptz(6),
    "created_at" timestamptz(6) DEFAULT now(),
    "updated_at" timestamptz(6) DEFAULT now(),
    "exam_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
    "batch_no" varchar COLLATE "pg_catalog"."default"
);

comment on column {schema}.paper_scans.exam_type is '考试类型';

comment on column {schema}.paper_scans.batch_no is '批次号';

alter table {schema}.paper_scans
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_abnormal
    on {schema}.paper_scans (is_abnormal);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_exam
    on {schema}.paper_scans (exam_id);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_needs_review
    on {schema}.paper_scans (needs_review);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_status
    on {schema}.paper_scans (status);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_student
    on {schema}.paper_scans (student_id);

CREATE TABLE IF NOT EXISTS {schema}.paper_scan_pages
(
    id               uuid                     default gen_random_uuid() not null
    constraint paper_scan_files_pkey
    primary key,
    paper_scan_id    uuid,
    page_num         integer                                            not null,
    file_name        varchar(200),
    file_url         text                                               not null,
    rectify_url      text,
    minio_bucket     varchar(100),
    minio_object_key varchar(500),
    file_size        bigint,
    scan_quality     integer
    constraint paper_scan_files_scan_quality_check
    check ((scan_quality >= 1) AND (scan_quality <= 10))
    constraint scan_pages_scan_quality_check
    check ((scan_quality >= 1) AND (scan_quality <= 10)),
    is_duplicate     boolean                  default false,
    is_blank         boolean                  default false,
    is_abnormal      boolean                  default false,
    abnormal_reason  text,
    result           jsonb,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now()
    );

alter table {schema}.paper_scan_pages
    owner to deep_mate;

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_files_abnormal
    on {schema}.paper_scan_pages (is_abnormal);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_files_blank
    on {schema}.paper_scan_pages (is_blank);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_files_duplicate
    on {schema}.paper_scan_pages (is_duplicate);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_files_page
    on {schema}.paper_scan_pages (page_num);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_files_quality
    on {schema}.paper_scan_pages (scan_quality);

CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_files_scan
    on {schema}.paper_scan_pages (paper_scan_id);

CREATE TABLE {schema}."paper_scan_block" (
                                                      "id" uuid NOT NULL,
                                                      "paper_scan_page_id" uuid NOT NULL,
                                                      "score" numeric(10,2) DEFAULT 0,
                                                      "answer_block_url" varchar COLLATE "pg_catalog"."default",
                                                      "answer_content" text COLLATE "pg_catalog"."default",
                                                      "status" varchar COLLATE "pg_catalog"."default",
                                                      "abnormal_reason" text COLLATE "pg_catalog"."default",
                                                      "answer_block_group_id" uuid,
                                                      "serial_number" int4 NOT NULL DEFAULT 0,
                                                      CONSTRAINT "paper_scan_block_unique" UNIQUE ("id")
);

ALTER TABLE {schema}."paper_scan_block"
    OWNER TO "deep_mate";

COMMENT ON COLUMN {schema}."paper_scan_block"."id" IS '主键ID';

COMMENT ON COLUMN {schema}."paper_scan_block"."paper_scan_page_id" IS '纸张页ID';

COMMENT ON COLUMN {schema}."paper_scan_block"."score" IS '分值';

COMMENT ON COLUMN {schema}."paper_scan_block"."answer_block_url" IS '作答题块切图URL';

COMMENT ON COLUMN {schema}."paper_scan_block"."answer_content" IS '作答内容';

COMMENT ON COLUMN {schema}."paper_scan_block"."status" IS '状态';

COMMENT ON COLUMN {schema}."paper_scan_block"."abnormal_reason" IS '异常原因';

COMMENT ON COLUMN {schema}."paper_scan_block"."answer_block_group_id" IS '所属题块组';

COMMENT ON COLUMN {schema}."paper_scan_block"."serial_number" IS '题块顺序';

COMMENT ON TABLE {schema}."paper_scan_block" IS '纸张扫描题块表';