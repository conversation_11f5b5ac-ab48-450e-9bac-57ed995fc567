use crate::model::grading::grading::ScanQueryParams;
use crate::model::grading::paper_scans::{PaperScan, PaperScanPageRecord, PaperScanStatus, PaperScansStatusSummaryRecord, UpdatePaperScan};
use crate::service::permission::{CasbinPermissionService, DataFilterManager, FilterContext};
use anyhow::anyhow;
use chrono::Local;
use sqlx::{PgPool, Postgres, QueryBuilder, Transaction};
use tracing::debug;
use tracing::log::info;
use uuid::Uuid;

/// 纸张扫描相关数据访问层
/// 作者: 萧达光
pub struct PaperScansRepository {}

impl PaperScansRepository {
    /// 保存纸张信息 带事务
    pub async fn save_tx(tx: &mut Transaction<'_, Postgres>, paper_scan: PaperScan) -> anyhow::Result<PaperScan> {
        // 1. 插入纸张主表信息
        let query = r#"
                INSERT INTO paper_scans (
                    id, exam_id,exam_type, batch_no, scan_method, scan_device,
                    status, duplicate_num, blank_num,
                    is_abnormal
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
                ) RETURNING *
                "#
        .to_string();

        let paper_scan = sqlx::query_as::<_, PaperScan>(&query)
            .bind(paper_scan.id)
            .bind(paper_scan.exam_id)
            .bind(paper_scan.exam_type.clone())
            .bind(paper_scan.batch_no.clone())
            .bind(paper_scan.scan_method.clone())
            .bind(paper_scan.scan_device.clone())
            .bind(paper_scan.status.clone())
            .bind(0)
            .bind(0)
            .bind(false)
            .fetch_one(tx.as_mut())
            .await?;

        Ok(paper_scan)
    }
    /// 更新纸张状态
    pub async fn update_paper_scans_status(db: &PgPool, schema_name: &str, leaf_ids: Vec<Uuid>, status: &PaperScanStatus, abnormal_reason: &Option<String>) -> anyhow::Result<u64> {
        if leaf_ids.is_empty() {
            return Ok(0);
        }

        let update_query = format!("UPDATE {}.paper_scans SET ", schema_name);

        let mut builder = sqlx::QueryBuilder::new(update_query);

        if let Some(abnormal_reason) = abnormal_reason {
            builder.push(" abnormal_reason = ").push_bind(abnormal_reason).push(" , ");
        }

        builder.push("  status = ").push_bind(status);

        // 根据状态激活相应的状态位
        match status {
            PaperScanStatus::Error => {
                builder.push(" ,is_abnormal =").push_bind(true);
            }
            _ => {
                builder.push(" ,is_abnormal =").push_bind(false);
            }
        }
        builder.push(" ,updated_at = ").push_bind(Local::now());
        builder.push(" WHERE id = ANY( ").push_bind(leaf_ids).push(" ) ");
        let count = builder.build().execute(db).await?.rows_affected();

        Ok(count)
    }

    /// 保存纸张信息
    pub async fn _save(db: &PgPool, schema_name: &str, paper_scan: PaperScan) -> anyhow::Result<PaperScan> {
        // 1. 插入纸张主表信息
        let query = format!(
            r#"
                INSERT INTO {}.paper_scans (
                    id, exam_id,exam_type, batch_no, scan_method, scan_device,
                    status, duplicate_num, blank_num,
                    is_abnormal
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
                ) RETURNING *
                "#,
            schema_name
        );

        let paper_scan = sqlx::query_as::<_, PaperScan>(&query)
            .bind(paper_scan.id)
            .bind(paper_scan.exam_id)
            .bind(paper_scan.exam_type.clone())
            .bind(paper_scan.batch_no.clone())
            .bind(paper_scan.scan_method.clone())
            .bind(paper_scan.scan_device.clone())
            .bind(paper_scan.status.clone())
            .bind(0)
            .bind(0)
            .bind(false)
            .fetch_one(db)
            .await?;

        Ok(paper_scan)
    }

    /// 重新绑定纸张与学生关系
    pub async fn rebind_student_to_leaf(db: &PgPool, schema_name: &str, exam_id: Uuid, student_id: Uuid, student_number: String, leaf_ids: Vec<Uuid>) -> anyhow::Result<i64> {
        // 1. 参数校验
        if leaf_ids.is_empty() {
            return Ok(0); // 空列表直接返回，避免无效查询
        }

        let update_query = format!("UPDATE {}.paper_scans SET student_id = ", schema_name);
        let mut builder = sqlx::QueryBuilder::new(update_query);
        builder.push_bind(student_id);
        builder.push(",student_number = ").push_bind(student_number);
        builder.push(" WHERE exam_id = ").push_bind(exam_id);
        builder.push(" AND id = ANY ( ").push_bind(leaf_ids).push(" ) ");

        let count: i64 = builder.build().execute(db).await?.rows_affected() as i64;
        Ok(count)
    }

    ///更新某个
    pub async fn mark_paper_scan_as_status_done(db: &PgPool, schema_name: &str, leaf_ids: Vec<Uuid>, page_ids: Vec<Uuid>) -> anyhow::Result<u64> {
        // 更新
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.paper_scans SET ", schema_name));
        builder.push(" status = ").push_bind(PaperScanStatus::Done);
        builder.push(" WHERE id = ANY(").push_bind(leaf_ids).push(")");
        builder.build().execute(db).await?;

        // 更新
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.paper_scan_pages SET ", schema_name));
        builder.push(" is_duplicate = ").push_bind(false);
        builder.push(" WHERE id = ANY(").push_bind(page_ids).push(")");
        let count = builder.build().execute(db).await?.rows_affected();

        Ok(count)
    }

    /// 批量更新纸张状态
    pub async fn batch_update_paper_scans_status(db: &PgPool, schema_name: &str, status: PaperScanStatus, leaf_ids: Vec<Uuid>) -> anyhow::Result<u64> {
        let update_query = format!(" UPDATE {}.paper_scans SET status = ", schema_name);

        let mut builder = sqlx::QueryBuilder::new(update_query);
        builder.push_bind(status);
        builder.push(" WHERE id = ANY(").push_bind(leaf_ids).push(")");

        let count = builder.build().execute(db).await?.rows_affected();
        Ok(count)
    }

    /// 更新纸张信息
    pub async fn update_paper_scans(db: &PgPool, schema_name: &str, paper_scan: UpdatePaperScan) -> anyhow::Result<i64> {
        // 更新纸张信息
        let update_query = format!(
            r#"
            UPDATE {}.paper_scans
            SET status = $1 , updated_at = NOW(), student_id = $2, student_number = $3, result = $4, duplicate_num = $5,
            blank_num = $6, is_abnormal = $7, abnormal_reason = $8
            WHERE id = $9
            "#,
            schema_name
        );

        sqlx::query(&update_query)
            .bind(paper_scan.status)
            .bind(paper_scan.student_id)
            .bind(paper_scan.student_number)
            .bind(paper_scan.result.clone())
            .bind(paper_scan.duplicate_num)
            .bind(paper_scan.blank_num)
            .bind(paper_scan.is_abnormal)
            .bind(paper_scan.abnormal_reason.clone())
            .bind(paper_scan.id)
            .execute(db)
            .await?;
        Ok(1)
    }

    /// 获取学科组分组的扫描卡数(学科组ID,学科组代码,学科组名称,扫描纸张数)
    pub async fn subject_group_scan_total_cards(
        db: &PgPool,
        filter_context: &FilterContext,
        data_filter_manager: &DataFilterManager,
        casbin_service: &dyn CasbinPermissionService,
    ) -> anyhow::Result<Vec<(Uuid, String, String, i64)>> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        let mut query_builder = QueryBuilder::new(&format!("SELECT sg.id,sg.subject_code ,sg.group_name,COUNT(ps.exam_id) FROM {}.subject_groups sg INNER JOIN  {}.homework h ON h.subject_group_id = sg.id INNER JOIN {}.paper_scans ps ON ps.exam_id  = h.id WHERE 1=1", filter_context.schema_name, filter_context.schema_name, filter_context.schema_name));

        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow!("Failed to apply data filter: {}", e))?;
        // 添加分组条件
        //GROUP BY sg.id,sg.subject_code ,sg.group_name
        query_builder.push(" GROUP BY sg.id,sg.subject_code,sg.group_name");

        // 执行查询
        info!("Executing homework query: {}", query_builder.sql());
        let rows: Vec<(Uuid, String, String, i64)> = query_builder.build_query_as::<(Uuid, String, String, i64)>().fetch_all(db).await?;

        Ok(rows)
    }

    pub async fn count_by_homework_id(db: &PgPool, filter_context: &FilterContext, data_filter_manager: &DataFilterManager, casbin_service: &dyn CasbinPermissionService) -> anyhow::Result<i64> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        let mut query_builder = sqlx::QueryBuilder::new(&format!(
            "SELECT COUNT(*) FROM {}.paper_scans ps inner join {}.homework h on h.id = ps.exam_id",
            filter_context.schema_name, filter_context.schema_name
        ));

        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow!("Failed to apply data filter: {}", e))?;
        // 执行查询
        info!("Executing homework query: {}", query_builder.sql());
        let count = query_builder.build_query_scalar().fetch_one(db).await?;
        Ok(count)
    }

    /// 批量删除相关的纸张表(paper_scans)中的记录
    pub async fn batch_delete_paper_scans(tx: &mut Transaction<'_, Postgres>, paper_scan_ids: &Vec<Uuid>) -> anyhow::Result<u64> {
        if paper_scan_ids.len() == 0 {
            // return Err(anyhow!("paper_scan_ids 不能为空"));
            return Ok(0);
        }

        let mut builder = sqlx::QueryBuilder::new("DELETE FROM paper_scans WHERE ");

        builder.push(" id = ANY( ").push_bind(paper_scan_ids).push(")");
        let count = builder.build().execute(tx.as_mut()).await?.rows_affected();

        Ok(count)
    }

    pub async fn page_all_paper_scan(db: &PgPool, schema_name: &str, params: &ScanQueryParams) -> anyhow::Result<(Vec<PaperScan>, i64)> {
        // 提取参数
        let ScanQueryParams { exam_id, batch_number, status, .. } = params;

        let page = params.page.unwrap_or(1).max(1); // 确保 ≥1
        let page_size = params.page_size.unwrap_or(10).clamp(1, 100); // 限制1-100
        let offset = (page - 1) * page_size;

        let query = format!("SELECT ps.* FROM {}.paper_scans ps WHERE 1=1 ", schema_name);
        let count_query = format!("SELECT COUNT(1) FROM {}.paper_scans ps WHERE 1=1 ", schema_name);
        let mut query_builder = sqlx::QueryBuilder::new(query);
        let mut count_builder = sqlx::QueryBuilder::new(count_query);

        // 添加查询条件
        if let Some(exam_id) = exam_id {
            query_builder.push(" AND ps.exam_id = ").push_bind(exam_id);
            count_builder.push(" AND ps.exam_id = ").push_bind(exam_id);
        }

        if let Some(batch_number) = batch_number {
            if !batch_number.is_empty() {
                query_builder.push(" AND ps.batch_no = ").push_bind(batch_number);
                count_builder.push(" AND ps.batch_no = ").push_bind(batch_number);
            }
        }

        if let Some(status) = status {
            // 转换为字符串数组
            let status_strs: Vec<&str> = status
                .iter()
                .map(|s| match s {
                    PaperScanStatus::Undistributed => "Undistributed",
                    PaperScanStatus::Unbound => "Unbound",
                    PaperScanStatus::Duplicate => "Duplicate",
                    PaperScanStatus::Error => "Error",
                    PaperScanStatus::Done => "Done",
                })
                .collect();

            query_builder.push(" AND ps.status = ANY( ").push_bind(status_strs.clone()).push(" ) ");
            count_builder.push(" AND ps.status = ANY( ").push_bind(status_strs.clone()).push(" ) ");
        }
        // 添加分页和排序
        query_builder.push(" ORDER BY ps.created_at DESC ");
        query_builder.push(" LIMIT ").push_bind(page_size);
        query_builder.push(" OFFSET ").push_bind(offset);

        debug!("SQL:{}", query_builder.sql());

        let list = query_builder.build_query_as::<PaperScan>().fetch_all(db).await?;
        let count: i64 = count_builder.build_query_scalar().fetch_one(db).await?;

        Ok((list, count))
    }
    /// 根据考试ID和批次号查询纸张信息
    pub async fn find_all_paper_scans_pages_by_exam_id_batch_number(db: &PgPool, schema_name: &str, exam_id: Uuid, batch_number: Option<String>) -> anyhow::Result<Vec<PaperScanPageRecord>> {
        let query = format!(
            r#"
           SELECT ps.id,ps.exam_id , psp.id as page_id, ps.exam_type,ps.batch_no,ps.student_id ,ps.student_number,ps.status,psp.file_url ,psp.page_num ,psp.rectify_url ,
            psp.file_name,psp.is_duplicate ,psp.is_blank ,psp.is_abnormal,psp.abnormal_reason,ps.created_at
             FROM {}.paper_scans ps
             INNER JOIN {}.paper_scan_pages psp  ON psp.paper_scan_id = ps.id
            WHERE
         "#,
            schema_name, schema_name
        );

        let mut builder = sqlx::QueryBuilder::new(query);

        builder.push(" ps.exam_id = ").push_bind(exam_id);

        if let Some(batch_number) = batch_number {
            builder.push(" AND ps.batch_no = ").push_bind(batch_number);
        }

        let list = builder.build_query_as().fetch_all(db).await?;

        Ok(list)
    }

    /// 更新纸张状态,将关联的纸张标记为重复纸张
    pub async fn mark_paper_scan_as_duplicate_status(db: &PgPool, schema_name: &str, leaf_ids: Vec<Uuid>) -> anyhow::Result<()> {
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.paper_scans SET status = ", schema_name));

        builder.push_bind(PaperScanStatus::Duplicate);
        builder.push(" WHERE id = ANY(").push_bind(leaf_ids).push(" ) ");
        builder.build().execute(db).await?;
        Ok(())
    }
    /// 根据考试信息查询批次号列表
    pub async fn find_paper_scans_pages_by_exam_id(db: &PgPool, schema_name: &str, exam_id: Uuid) -> anyhow::Result<Vec<PaperScanPageRecord>> {
        // 查询语句
        let query_str = format!(
            r#"
           SELECT ps.id,ps.exam_id , psp.id as page_id, ps.exam_type,ps.batch_no,ps.student_id ,ps.student_number,ps.status ,psp.file_url ,psp.page_num ,psp.rectify_url ,
            psp.file_name,psp.is_duplicate ,psp.is_blank ,psp.is_abnormal,psp.abnormal_reason,ps.created_at
             FROM {}.paper_scans ps
             INNER JOIN {}.paper_scan_pages psp  ON psp.paper_scan_id = ps.id
            WHERE ps.exam_id =
         "#,
            schema_name, schema_name
        );
        let mut builder = sqlx::QueryBuilder::new(query_str);
        builder.push_bind(exam_id).push(" ORDER BY ps.created_at DESC");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    /// 统计试卷扫描汇总信息
    pub async fn calculate_scan_statistics(db: &PgPool, schema_name: &str, exam_id: Uuid) -> anyhow::Result<Vec<PaperScansStatusSummaryRecord>> {
        let query = format!(
            " SELECT ps.status ,count(1) as total FROM {}.paper_scans ps INNER JOIN {}.paper_scan_pages psp ON
	            psp.paper_scan_id = ps.id WHERE ps.exam_id = ",
            schema_name, schema_name
        );
        let mut builder = sqlx::QueryBuilder::new(&query);
        builder.push_bind(exam_id);
        builder.push(" group by ps.status ");

        let rows = builder.build_query_as().fetch_all(db).await?;

        Ok(rows)
    }

    pub async fn get_sheet_pages_by_id(db: &PgPool, schema_name: &str, homework_id: Uuid, sheet_id: Option<Uuid>, student_id: Option<Uuid>) -> anyhow::Result<Vec<PaperScanPageRecord>> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            r#"
            SELECT ps.id,ps.exam_id,psp.id as page_id,ps.exam_type,ps.batch_no,ps.student_id ,ps.student_number,ps.status ,psp.file_url ,psp.page_num ,psp.rectify_url ,
            psp.file_name,psp.is_duplicate ,psp.is_blank ,psp.is_abnormal,psp.abnormal_reason,ps.created_at
            FROM {}.paper_scans ps
            INNER JOIN {}.paper_scan_pages psp  ON psp.paper_scan_id = ps.id
            WHERE ps.exam_id =
            "#,
            schema_name, schema_name
        ));
        builder.push_bind(homework_id);
        if let Some(sheet_id) = sheet_id {
            builder.push(" AND ps.id = ").push_bind(sheet_id);
        }
        if let Some(student_id) = student_id {
            builder.push(" AND ps.student_id = ").push_bind(student_id);
        }
        builder.push(" ORDER BY psp.page_num");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }
}
