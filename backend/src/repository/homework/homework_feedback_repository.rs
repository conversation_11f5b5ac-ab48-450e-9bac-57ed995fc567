use sqlx::{PgP<PERSON>, Postgres};
use uuid::Uuid;
use crate::model::homework::homework_feedback::{HomeworkFeedback, HomeworkFeedbackStatus};
use crate::model::PageParams;
use crate::utils::db::builder_in_list;

pub struct HomeworkFeedbackRepository;
impl HomeworkFeedbackRepository {
    pub async fn create_feedback(db: &PgPool, tenant_name: &str, homework_id: Uuid, student_id: Uuid, score_id: Option<Uuid>, text: String,) -> anyhow::Result<HomeworkFeedback> {
        let query = format!("INSERT INTO {}.homework_feedback (homework_id, student_id, score_id, text, status)", tenant_name);
        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push(" VALUES (").push_bind(homework_id)
            .push(", ").push_bind(student_id)
            .push(", ").push_bind(score_id)
            .push(", ").push_bind(text)
            .push(", ").push_bind(HomeworkFeedbackStatus::Initial)
            .push(")  RETURNING *");
        // 插入HomeworkFeedback并反馈结构
        let ret = builder.build_query_as().fetch_one(db).await?;
        Ok(ret)
    }
    pub async fn update_feedback(db: &PgPool, tenant_name: &str, id: Uuid, status: HomeworkFeedbackStatus, text: Option<String>) -> anyhow::Result<()> {
        let query = format!("UPDATE {}.homework_feedback SET status = ", tenant_name);
        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push_bind(status);
        if let Some(text) = text {
            builder.push(", text = ").push_bind(text);
        }
        builder.push(" WHERE id = ").push_bind(id);
        builder.build().execute(db).await?;
        Ok(())
    }
    pub async fn fetch_feedbacks(db: &PgPool, tenant_name: &str, homework_id: Uuid, student_id: Uuid) -> anyhow::Result<Vec<HomeworkFeedback>> {
        let query = format!("SELECT * FROM {}.homework_feedback WHERE ", tenant_name);
        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push("homework_id = ").push_bind(homework_id)
            .push("AND student_id = ").push_bind(student_id);
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }
    fn gen_builder_feedbacks<'a>(is_count: bool, schema_name: &str, homework_ids: Vec<Uuid>, mut status_list: Vec<HomeworkFeedbackStatus>) -> sqlx::QueryBuilder<'a, Postgres> {
        let fetch_str = if is_count {"COUNT(*)"} else { "*" };
        let query = format!("SELECT {} FROM {}.homework_feedback WHERE ", fetch_str, schema_name);
        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push("homework_id = ANY(").push_bind(homework_ids).push(") ");
        if !status_list.is_empty() {
            builder.push(" AND status ");
            builder_in_list(&mut builder, status_list).ok();
        }
        builder
    }
    pub async fn fetch_page_feedbacks(db: &PgPool, schema_name: &str, homework_ids: Vec<Uuid>, status_list: Vec<HomeworkFeedbackStatus>, page_params: &PageParams) -> anyhow::Result<(Vec<HomeworkFeedback>, i64)> {
        let mut builder = Self::gen_builder_feedbacks(true, schema_name, homework_ids.clone(), status_list.clone());
        let count = builder.build_query_scalar().fetch_one(db).await?;
        let mut builder = Self::gen_builder_feedbacks(false, schema_name, homework_ids, status_list);
        builder.push(" LIMIT ").push_bind(page_params.get_limit())
            .push(" OFFSET ").push_bind(page_params.get_offset());
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok((ret, count))
    }
    pub async fn feedback_statistics(db: &PgPool, tenant_name: &str, homework_ids: Vec<Uuid>) -> anyhow::Result<Vec<(Uuid, HomeworkFeedbackStatus, i64)>> {
        let query = format!("SELECT homework_id, status, COUNT(*) as count FROM {}.homework_feedback WHERE ", tenant_name);
        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push("homework_id = ANY(").push_bind(homework_ids).push(") ")
            .push("GROUP BY homework_id, status");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }
}