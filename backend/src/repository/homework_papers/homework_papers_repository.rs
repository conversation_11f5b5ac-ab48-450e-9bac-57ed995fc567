use crate::model::homework::homework::Homework;
use crate::model::paper::paper::Paper;
use sqlx::{pool::PoolConnection, PgPool, Postgres};
use uuid::Uuid;

/**
 * 作者：张瀚
 * 说明：试卷和作业关联表的数据库方法
 */
pub struct HomeworkPapersRepository {}

impl HomeworkPapersRepository {
    /**
     * 作者：张瀚
     * 说明：通过试卷ID查询关联的作业对象，理应只有一个
     */
    pub async fn find_homework_list_by_paper_id(mut conn: PoolConnection<Postgres>, paper_id: Uuid) -> Result<Vec<Homework>, String> {
        sqlx::query_as::<_, Homework>("SELECT h.* FROM homework h,homework_papers hp WHERE hp.paper_id = $1 AND h.id = hp.homework_id ORDER BY h.created_at DESC")
            .bind(paper_id)
            .fetch_all(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：通过作业查询的试卷信息，可能会有多条，正常就0-1
     */
    pub async fn fetch_paper_by_homework_id(db: &PgPool, schema_name: &str, homework_id: Uuid) -> Result<Vec<Paper>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT p.* FROM {}.papers p, {}.homework_papers hp WHERE hp.homework_id = ", schema_name, schema_name).as_str());
        builder.push_bind(homework_id).push(" AND hp.paper_id = p.id ORDER BY p.created_at DESC ");
        let papers = builder.build_query_as::<Paper>().fetch_all(db).await.map_err(|e| e.to_string())?;
        Ok(papers)
    }
    pub async fn get_homework_total_page(db: &PgPool, schema_name: &str, homework_id: Uuid) -> Result<i32, String> {
        let mut papers = Self::fetch_paper_by_homework_id(db, schema_name, homework_id).await?;
        let paper = papers.remove(0);
        Ok(paper.paper_content.0.answer_card.page_total)
    }

    /**
     * 作者：张瀚
     * 说明：替换所有指定试卷ID为新的ID
     */
    pub async fn replace_paper_id(conn: &mut PoolConnection<Postgres>, old_paper_id: &Uuid, new_paper_id: &Uuid) -> Result<(), String> {
        sqlx::query("UPDATE homework_papers SET paper_id = $1 WHERE paper_id = $2")
            .bind(new_paper_id)
            .bind(old_paper_id)
            .execute(&mut **conn)
            .await
            .map(|_| ())
            .map_err(|e| e.to_string())
    }
}
