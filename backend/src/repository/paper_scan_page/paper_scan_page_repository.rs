use crate::model::grading::grading::ScanQueryParams;
use crate::model::grading::paper_scan_pages::{PaperScanPage, PaperScanPagesRecord, UpdatePaperScanPages};
use crate::model::grading::paper_scans::PaperScanStatus;
use crate::service::permission::{CasbinPermissionService, DataFilterManager, FilterContext};
use anyhow::anyhow;
use chrono::{DateTime, Local, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::{FromRow, PgPool, Postgres, QueryBuilder, Transaction};
use tracing::debug;
use tracing::log::info;
use uuid::Uuid;

/// 试卷纸张页信息
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScanPageVo {
    pub batch_number: Option<String>,
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub status: PaperScanStatus,
    pub id: Uuid,
    pub paper_scan_id: Uuid,
    pub page_num: i32,
    pub file_name: String,
    pub file_url: String,
    pub rectify_url: Option<String>,
    pub minio_bucket: Option<String>,
    pub minio_object_key: Option<String>,
    pub file_size: i64,
    pub scan_quality: Option<i32>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub result: Option<Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 纸张扫描页面相关数据访问层
/// 作者: 萧达光
pub struct PaperScanPageRepository {}

impl PaperScanPageRepository {
    /// 保存试卷扫描图片页码信息 带事务执行
    pub async fn save_tx(tx: &mut Transaction<'_, Postgres>, paper_scan_page: PaperScanPage) -> anyhow::Result<PaperScanPage> {
        let query = r#"
                INSERT INTO paper_scan_pages (
                    id,paper_scan_id,page_num,file_name, file_url,minio_bucket,minio_object_key, file_size, is_duplicate, is_blank,
                    is_abnormal, abnormal_reason, result,
                    created_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
                ) RETURNING *
                "#
        .to_string();

        let scan = sqlx::query_as::<_, PaperScanPage>(&query)
            .bind(paper_scan_page.id)
            .bind(paper_scan_page.paper_scan_id)
            .bind(paper_scan_page.page_num)
            .bind(paper_scan_page.file_name.clone())
            .bind(paper_scan_page.file_url.clone())
            .bind(paper_scan_page.minio_bucket.clone())
            .bind(paper_scan_page.minio_object_key.clone())
            .bind(paper_scan_page.file_size)
            .bind(paper_scan_page.is_duplicate)
            .bind(paper_scan_page.is_blank)
            .bind(paper_scan_page.is_abnormal)
            .bind(paper_scan_page.abnormal_reason.clone())
            .bind(paper_scan_page.result.clone())
            .bind(paper_scan_page.created_at)
            .fetch_one(tx.as_mut())
            .await?;

        Ok(scan)
    }
    /// 保存试卷扫描图片页码信息
    pub async fn save(db: &PgPool, schema_name: &str, paper_scan_page: PaperScanPage) -> anyhow::Result<PaperScanPage> {
        let query = format!(
            r#"
                INSERT INTO {}.paper_scan_pages (
                    id,paper_scan_id,page_num,file_name, file_url,minio_bucket,minio_object_key, file_size, is_duplicate, is_blank,
                    is_abnormal, abnormal_reason, result,rectify_url,
                    created_at
                ) VALUES ("#,
            schema_name
        );
        let mut builder = sqlx::QueryBuilder::new(query);
        builder
            .push_bind(paper_scan_page.id)
            .push(", ")
            .push_bind(paper_scan_page.paper_scan_id)
            .push(", ")
            .push_bind(paper_scan_page.page_num)
            .push(", ")
            .push_bind(paper_scan_page.file_name)
            .push(", ")
            .push_bind(paper_scan_page.file_url)
            .push(", ")
            .push_bind(paper_scan_page.minio_bucket)
            .push(", ")
            .push_bind(paper_scan_page.minio_object_key)
            .push(", ")
            .push_bind(paper_scan_page.file_size)
            .push(", ")
            .push_bind(paper_scan_page.is_duplicate)
            .push(", ")
            .push_bind(paper_scan_page.is_blank)
            .push(", ")
            .push_bind(paper_scan_page.is_abnormal)
            .push(", ")
            .push_bind(paper_scan_page.abnormal_reason)
            .push(", ")
            .push_bind(paper_scan_page.result)
            .push(", ")
            .push_bind(paper_scan_page.rectify_url)
            .push(", ")
            .push_bind(paper_scan_page.created_at)
            .push(") RETURNING *");
        let scan = builder.build_query_as().fetch_one(db).await?;
        Ok(scan)
    }
    pub async fn count_by_subject_group_id(
        db: &PgPool,
        filter_context: &FilterContext,
        data_filter_manager: &DataFilterManager,
        casbin_service: &dyn CasbinPermissionService,
    ) -> anyhow::Result<Vec<(Uuid, String, String, i64)>> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        let mut query_builder = QueryBuilder::new(&format!("SELECT sg.id,sg.subject_code ,sg.group_name,COUNT(psp.id) FROM {}.subject_groups sg INNER JOIN  {}.homework h ON h.subject_group_id = sg.id INNER JOIN {}.paper_scans ps ON ps.exam_id  = h.id INNER JOIN {}.paper_scan_pages psp ON psp.paper_scan_id = ps.id WHERE 1=1", filter_context.schema_name, filter_context.schema_name, filter_context.schema_name, filter_context.schema_name));

        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow!("Failed to apply data filter: {}", e))?;
        // 添加分组条件
        //GROUP BY sg.id,sg.subject_code ,sg.group_name
        query_builder.push(" GROUP BY sg.id,sg.subject_code ,sg.group_name");

        // 执行查询
        info!("Executing homework query: {}", query_builder.sql());
        let rows: Vec<(Uuid, String, String, i64)> = query_builder.build_query_as::<(Uuid, String, String, i64)>().fetch_all(db).await?;

        Ok(rows)
    }
    pub async fn count_by_homework_id(db: &PgPool, filter_context: &FilterContext, data_filter_manager: &DataFilterManager, casbin_service: &dyn CasbinPermissionService) -> anyhow::Result<i64> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }
        //select count(*) from tenant_zhanghan.paper_scans ps
        // inner join tenant_zhanghan.paper_scan_pages psp on psp.paper_scan_id = ps.id
        //
        let query = format!(
            "SELECT count(*) FROM {}.paper_scans ps
                INNER JOIN {}.homework h on h.id = ps.exam_id
                INNER JOIN {}.paper_scan_pages psp on psp.paper_scan_id = ps.id WHERE 1=1 ",
            filter_context.schema_name, filter_context.schema_name, filter_context.schema_name
        );

        let mut query_builder = QueryBuilder::new(query);
        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow!("Failed to apply data filter: {}", e))?;
        // 执行查询
        info!("Executing query: {}", query_builder.sql());
        let row = query_builder.build_query_scalar().fetch_one(db).await?;

        Ok(row)
    }

    pub async fn fetch_scan_page_by_id(db: &PgPool, schema_name: &str, page_id: Uuid) -> anyhow::Result<PaperScanPage> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.paper_scan_pages WHERE id=", schema_name));
        let ret = builder.push_bind(page_id).build_query_as().fetch_one(db).await?;
        Ok(ret)
    }

    /// 更新纸张中页面信息
    /// 作者: 萧达光
    pub async fn update_paper_scan_page(db: &PgPool, schema_name: &str, paper_scan_pages: UpdatePaperScanPages) -> anyhow::Result<u64> {
        // 更新纸张页码信息
        let update_query = format!(
            r#"
            UPDATE {}.paper_scan_pages
            SET  updated_at = NOW() "#,
            schema_name
        );
        let mut builder = sqlx::QueryBuilder::new(update_query);
        builder
            .push(", page_num = ")
            .push_bind(paper_scan_pages.page_num)
            .push(", rectify_url = ")
            .push_bind(paper_scan_pages.rectify_url)
            .push(", is_duplicate = ")
            .push_bind(paper_scan_pages.is_duplicate)
            .push(", is_blank = ")
            .push_bind(paper_scan_pages.is_blank)
            .push(", is_abnormal = ")
            .push_bind(paper_scan_pages.is_abnormal)
            .push(", abnormal_reason = ")
            .push_bind(paper_scan_pages.abnormal_reason)
            .push(", result = ")
            .push_bind(paper_scan_pages.result)
            .push(" WHERE id = ")
            .push_bind(paper_scan_pages.id);
        let result = builder.build().execute(db).await?;
        Ok(result.rows_affected())
    }

    /// 更新
    pub async fn update_scan_page_blank(db: &PgPool, schema_name: &str, page_id: Uuid) -> anyhow::Result<u64> {
        let update_query = format!("UPDATE {}.paper_scan_pages SET ", schema_name);
        let mut builder = QueryBuilder::new(update_query);
        builder.push(" is_blank =").push_bind(true).push(" , ");
        builder.push(" is_abnormal =").push_bind(false).push(" , ");
        builder.push(" updated_at = ").push_bind(Local::now());
        builder.push(" WHERE id = ").push_bind(page_id);
        let result = builder.build().execute(db).await?;
        Ok(result.rows_affected())
    }
    pub async fn update_paper_scan_page_status(db: &PgPool, schema_name: &str, leaf_ids: Vec<Uuid>, status: &PaperScanStatus, abnormal_reason: &Option<String>) -> anyhow::Result<u64> {
        if leaf_ids.is_empty() {
            return Ok(0);
        }
        let update_query = format!("UPDATE {}.paper_scan_pages SET ", schema_name);
        let mut builder = QueryBuilder::new(update_query);
        if let Some(abnormal_reason) = abnormal_reason {
            builder.push(" abnormal_reason = ").push_bind(abnormal_reason).push(" , ");
        }
        builder.push(" updated_at = ").push_bind(Local::now());
        // 根据状态激活相应的状态位
        match status {
            PaperScanStatus::Duplicate => {
                builder.push(" ,is_duplicate =").push_bind(true);
                builder.push(" ,is_blank =").push_bind(false);
                builder.push(" ,is_abnormal =").push_bind(false);
            }
            PaperScanStatus::Error => {
                builder.push(" ,is_duplicate =").push_bind(false);
                builder.push(" ,is_blank =").push_bind(false);
                builder.push(" ,is_abnormal =").push_bind(true);
            }
            _ => {
                builder.push(" ,is_duplicate =").push_bind(false);
                builder.push(" ,is_blank =").push_bind(false);
                builder.push(" ,is_abnormal =").push_bind(false);
            }
        }

        builder.push(" WHERE paper_scan_id = ANY( ").push_bind(leaf_ids).push(" )");
        let count = builder.build().execute(db).await?.rows_affected();
        Ok(count)
    }

    pub async fn fetch_leaf_ids_scan_pages(db: &PgPool, schema_name: &str, page_ids: Vec<Uuid>) -> anyhow::Result<Vec<Uuid>> {
        let query_str = format!("SELECT paper_scan_id FROM {}.paper_scan_pages WHERE id = ANY(", schema_name);
        let mut builder = sqlx::QueryBuilder::new(query_str);
        builder.push_bind(page_ids).push(")");
        let ret = builder.build_query_scalar().fetch_all(db).await?;
        Ok(ret)
    }
    /// 查询纸张与页面信息
    pub async fn fetch_paper_scan_pages(db: &PgPool, schema_name: &str, exam_id: Uuid, batch_number: Option<String>, leaf_ids: Option<Vec<Uuid>>) -> anyhow::Result<Vec<PaperScanPagesRecord>> {
        let query = format!("SELECT ps.exam_id,ps.id as paper_scan_id ,ps.exam_type,ps.student_id,ps.exam_type,ps.student_number,psp.id as paper_scan_pages_id,psp.page_num,psp.file_url as paper_scan_page_file_url FROM {}.paper_scans ps
                    INNER JOIN {}.paper_scan_pages psp ON ps.id = psp.paper_scan_id
                    WHERE ", schema_name, schema_name);
        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push(" ps.exam_id = ").push_bind(exam_id);
        if let Some(batch_number) = batch_number {
            if !batch_number.is_empty() {
                builder.push(" AND ps.batch_no = ").push_bind(batch_number);
            }
        }

        if let Some(leaf_ids) = leaf_ids {
            builder.push(" AND psp.paper_scan_id = ANY( ").push_bind(leaf_ids).push(") ");
        }

        let pages: Vec<PaperScanPagesRecord> = builder.build_query_as().fetch_all(db).await?;
        Ok(pages)
    }
    pub async fn find_paper_scan_pages_by_batch_no(db: &PgPool, schema_name: &str, paper_scan_page: PaperScanPage) -> anyhow::Result<Vec<PaperScanPage>> {
        let query = format!("SELECT psp.* FROM {}.paper_scan_pages psp WHERE psp.paper_scan_id = $1 ORDER BY psp.page_num ASC", schema_name);
        let list = sqlx::query_as::<_, PaperScanPage>(query.as_str()).bind(paper_scan_page.id).fetch_all(db).await?;
        Ok(list)
    }

    /// 批量删除相关的页码表(paper_scan_pages)中的记录
    pub async fn batch_delete_paper_scan_pages(tx: &mut Transaction<'_, Postgres>, page_ids: &Vec<Uuid>) -> anyhow::Result<u64> {
        if page_ids.len() == 0 {
            // return Err(anyhow!("page_ids 不能为空"));
            return Ok(0);
        }

        let mut builder = sqlx::QueryBuilder::new("DELETE FROM paper_scan_pages WHERE ");

        builder.push(" id = ANY( ").push_bind(page_ids).push(")");
        let count = builder.build().execute(tx.as_mut()).await?.rows_affected();

        Ok(count)
    }

    /// 分页查询所有纸张信息
    pub async fn page_all_paper_scan_page(db: &PgPool, schema_name: &str, params: &ScanQueryParams) -> anyhow::Result<(Vec<PaperScanPageVo>, i64)> {
        // 提取参数
        let ScanQueryParams { exam_id, batch_number, status, .. } = params;
        let page = params.page.unwrap_or(1).max(1); // 确保 ≥1
        let page_size = params.page_size.unwrap_or(10).clamp(1, 100); // 限制1-100
        let offset = (page - 1) * page_size;

        let query = format!(
            "select ps.batch_no as batch_number ,ps.student_id ,ps.student_number,ps.status, psp.* from {}.paper_scans ps INNER JOIN {}.paper_scan_pages psp  on psp.paper_scan_id = ps.id
                      WHERE 1=1 ",
            schema_name, schema_name
        );
        let count_query = format!(
            "select count(1) from {}.paper_scans ps INNER JOIN {}.paper_scan_pages psp  on psp.paper_scan_id = ps.id
                      WHERE 1=1 ",
            schema_name, schema_name
        );
        let mut query_builder = sqlx::QueryBuilder::new(query);
        let mut count_builder = sqlx::QueryBuilder::new(count_query);

        // 添加查询条件
        if let Some(exam_id) = exam_id {
            query_builder.push(" AND ps.exam_id = ").push_bind(exam_id);
            count_builder.push(" AND ps.exam_id = ").push_bind(exam_id);
        }

        if let Some(batch_number) = batch_number {
            if !batch_number.is_empty() {
                query_builder.push(" AND ps.batch_no = ").push_bind(batch_number);
                count_builder.push(" AND ps.batch_no = ").push_bind(batch_number);
            }
        }

        if let Some(status) = status {
            // 转换为字符串数组
            let status_strs: Vec<&str> = status
                .iter()
                .map(|s| match s {
                    PaperScanStatus::Undistributed => "Undistributed",
                    PaperScanStatus::Unbound => "Unbound",
                    PaperScanStatus::Duplicate => "Duplicate",
                    PaperScanStatus::Error => "Error",
                    PaperScanStatus::Done => "Done",
                })
                .collect();

            query_builder.push(" AND ps.status = ANY( ").push_bind(status_strs.clone()).push(" ) ");
            count_builder.push(" AND ps.status = ANY( ").push_bind(status_strs.clone()).push(" ) ");
        }

        // 添加分页和排序
        query_builder.push(" order by psp.page_num ASC ");
        query_builder.push(" limit ").push_bind(page_size);
        query_builder.push(" offset ").push_bind(offset);

        let list = query_builder.build_query_as::<PaperScanPageVo>().fetch_all(db).await?;
        debug!("[{}],limit:{},offset:{},size:{}", query_builder.sql(), page_size, offset, list.len());

        let count: i64 = count_builder.build_query_scalar().fetch_one(db).await?;

        Ok((list, count))
    }

    /// 根据学生ID与考试ID和页码查询所有重复页记录
    pub async fn fetch_duplicate_paper_scan_pages(db: &PgPool, schema_name: &str, student_id: Uuid, homework_id: Uuid, page_number: i32) -> anyhow::Result<Vec<PaperScanPagesRecord>> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT  ps.exam_id,ps.id as paper_scan_id ,ps.exam_type,ps.student_id ,ps.student_number,psp.id as paper_scan_pages_id,psp.page_num,psp.file_url as paper_scan_page_file_url FROM {}.paper_scans ps
                    INNER JOIN {}.paper_scan_pages psp ON ps.id = psp.paper_scan_id
                    WHERE ", schema_name, schema_name),
        );

        builder.push(" ps.student_id = ").push_bind(student_id);
        builder.push(" AND ps.exam_id = ").push_bind(homework_id);
        builder.push(" AND psp.page_num = ").push_bind(page_number);

        let pages: Vec<PaperScanPagesRecord> = builder.build_query_as().fetch_all(db).await?;
        Ok(pages)
    }

    /// 根据考试ID 和纸张页面ID 查询页面信息
    pub async fn fetch_paper_scan_pages_by_page_id(db: &PgPool, schema_name: &str, homework_id: Uuid, page_ids: Vec<Uuid>) -> anyhow::Result<Vec<PaperScanPagesRecord>> {
        let mut builder = sqlx::QueryBuilder::new(
            format!("SELECT  ps.exam_id,ps.id as paper_scan_id ,ps.exam_type,ps.student_id,ps.student_number,psp.id as paper_scan_pages_id,psp.page_num,psp.file_url as paper_scan_page_file_url FROM {}.paper_scans ps
                    INNER JOIN {}.paper_scan_pages psp ON ps.id = psp.paper_scan_id
                    WHERE ", schema_name, schema_name)
        );

        builder.push(" ps.exam_id = ").push_bind(homework_id);
        builder.push(" AND psp.id = ANY( ").push_bind(page_ids).push(" ) ");

        let pages: Vec<PaperScanPagesRecord> = builder.build_query_as().fetch_all(db).await?;
        Ok(pages)
    }

    /// 根据纸张ID查询对应的页码
    pub async fn find_paper_scan_pages_by_paper_scan_id(db: &PgPool, schema_name: &str, paper_scan_ids: Vec<Uuid>) -> anyhow::Result<Vec<PaperScanPage>> {
        let query = format!("SELECT * FROM {}.paper_scan_pages WHERE ", schema_name);

        let mut builder = sqlx::QueryBuilder::new(query);
        builder.push(" paper_scan_id = ANY( ").push_bind(paper_scan_ids).push(" ) ");

        let list = builder.build_query_as().fetch_all(db).await?;
        Ok(list)
    }

    /// 标记页面为重复状态
    pub async fn mark_page_as_duplicate_status(db: &PgPool, schema_name: &str, page_ids: Vec<Uuid>) -> anyhow::Result<()> {
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.paper_scan_pages SET ", schema_name));
        builder.push(" is_duplicate = ").push_bind(true);
        builder.push(" WHERE id = ANY ( ").push_bind(page_ids).push(" ) ");
        debug!("sql:{}", builder.sql());
        builder.build().execute(db).await?;
        Ok(())
    }
    pub async fn fetch_scan_pages_by_student_id(db: &PgPool, tenant_name: &str, student_id: Uuid, homework_id: Uuid) -> anyhow::Result<Vec<PaperScanPagesRecord>> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT ps.exam_id,ps.id as paper_scan_id ,ps.exam_type,ps.student_id ,ps.student_number,psp.id as paper_scan_pages_id,psp.page_num,psp.file_url as paper_scan_page_file_url FROM {}.paper_scans ps
                    INNER JOIN {}.paper_scan_pages psp ON ps.id = psp.paper_scan_id
                    WHERE ",
            tenant_name, tenant_name
        ));
        builder.push(" ps.student_id = ").push_bind(student_id);
        builder.push(" AND ps.exam_id = ").push_bind(homework_id);
        builder.push(" AND psp.is_blank = ").push_bind(false);
        let pages: Vec<PaperScanPagesRecord> = builder.build_query_as().fetch_all(db).await.map_err(|e| anyhow!(e.to_string()))?;
        Ok(pages)
    }
}
