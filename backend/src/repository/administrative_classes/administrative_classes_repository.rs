use sqlx::PgPool;
use uuid::Uuid;
use anyhow::Result;

#[derive(Clone)]
pub struct AdministrativeClassesRepository {
    db_pool: PgPool,
}

impl AdministrativeClassesRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /// 只更新行政班级的 grade_level_code 字段
    /// 
    /// # Arguments
    /// * `schema_name` - 租户模式名称
    /// * `class_id` - 班级ID
    /// * `grade_level_code` - 新的年级代码
    /// 
    /// # Returns
    /// * `Result<()>` - 成功返回 Ok(()), 失败返回错误信息
    pub async fn update_grade_level_code(
        &self,
        schema_name: &str,
        class_id: Uuid,
        grade_level_code: &str,
    ) -> Result<()> {
        let update_query = format!(
            "UPDATE {}.administrative_classes SET grade_level_code = $1, updated_at = now() WHERE id = $2",
            schema_name
        );

        sqlx::query(&update_query)
            .bind(grade_level_code)
            .bind(class_id)
            .execute(&self.db_pool)
            .await?;

        Ok(())
    }
}
