use crate::service::permission::{CasbinPermissionService, DataFilterManager, FilterContext};
use anyhow::{anyhow, Result};
use sqlx::{PgPool, QueryBuilder};
use tracing::debug;
use uuid::Uuid;

/// 学科组数据访问层
#[derive(Clone)]
pub struct SubjectGroupRepository {
    db_pool: PgPool,
}

impl SubjectGroupRepository {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 根据教师ID获取其所属的学科组ID列表
    pub async fn find_subject_group_ids_by_teacher_id(&self, schema_name: &str, teacher_id: &Uuid) -> Result<Vec<Uuid>, sqlx::Error> {
        let query = format!("SELECT DISTINCT subject_group_id FROM {}.subject_group_members WHERE teacher_id = $1 AND is_active = true", schema_name);

        sqlx::query_scalar(&query).bind(teacher_id).fetch_all(&self.db_pool).await
    }

    /// 获取用户管理的学科组ID列表
    pub async fn get_user_managed_subject_groups(pool: &PgPool, user_id: &Uuid, schema_name: &str) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT DISTINCT sgm.subject_group_id
            FROM {}.subject_group_members sgm
            JOIN {}.teachers t ON sgm.teacher_id = t.id
            WHERE t.user_id = $1 AND sgm.is_active = true
            "#,
            schema_name, schema_name
        );

        let group_ids: Vec<Uuid> = sqlx::query_scalar(&query).bind(user_id).fetch_all(pool).await?;

        Ok(group_ids)
    }

    /// 获取学科组作业题卡消耗统计
    pub async fn subject_group_homework_consume_statistics(
        db: &PgPool,
        filter_context: &FilterContext,
        data_filter_manager: &DataFilterManager,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Vec<(Uuid, String, String, i64, i64, i64)>> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        // 使用QueryBuilder构建动态查询条件
        let mut query_builder = QueryBuilder::new(format!(
            "SELECT sg.id, sg.subject_code,sg.group_name, COALESCE(SUM(h.leaf_total), 0) as leaf_total, COALESCE(SUM(h.page_total), 0) as page_total, count(h.id) as homework_count FROM {}.subject_groups sg LEFT JOIN {}.homework h on h.subject_group_id = sg.id WHERE 1=1 ",
            filter_context.schema_name,filter_context.schema_name
        ));

        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow!("Failed to apply data filter: {}", e))?;

        // 添加分组参数
        query_builder.push(" GROUP BY sg.id,sg.subject_code, sg.group_name ");
        // 执行查询
        debug!("Executing query: {}", query_builder.sql());
        let row: Vec<(Uuid, String, String, i64, i64, i64)> = query_builder.build_query_as().fetch_all(db).await?;

        Ok(row)
    }
}
