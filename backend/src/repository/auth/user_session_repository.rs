use anyhow::{Context, Result};
use chrono::{Duration, Utc};
use ipnetwork::IpNetwork;
use sqlx::PgPool;
use std::net::IpAddr;
use uuid::Uuid;

use crate::model::user::auth::UserSession;

/// 用户会话数据访问层
pub struct UserSessionRepository;

impl UserSessionRepository {
    /// 创建新的用户会话
    pub async fn create_session(
        pool: &PgPool,
        user_id: Uuid,
        session_token: &str,
        refresh_token: &str,
        current_identity_id: Option<Uuid>,
        device_info: Option<&serde_json::Value>,
        ip_address: Option<IpAddr>,
        user_agent: Option<&str>,
    ) -> Result<Uuid, sqlx::Error> {
        let session_id = Uuid::new_v4();
        let now = Utc::now();
        let expires_at = now + Duration::hours(1);
        let refresh_expires_at = now + Duration::days(30);

        sqlx::query!(
            r#"
            INSERT INTO public.user_sessions
            (id, user_id, session_token, refresh_token, current_identity_id, device_info,
             ip_address, user_agent, expires_at, refresh_expires_at, last_activity_at, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#,
            session_id,
            user_id,
            session_token,
            refresh_token,
            current_identity_id,
            device_info,
            ip_address.map(|ip| IpNetwork::from(ip)),
            user_agent,
            expires_at,
            refresh_expires_at,
            now,
            now
        )
        .execute(pool)
        .await?;
        Ok(session_id)
    }

    /// 根据刷新令牌获取会话
    pub async fn get_session_by_refresh_token(
        pool: &PgPool,
        refresh_token: &str,
    ) -> Result<Option<UserSession>, sqlx::Error> {
        let session = sqlx::query_as::<_, UserSession>(
            "SELECT id, user_id, session_token, refresh_token, current_identity_id, available_identities, device_info, ip_address, user_agent, expires_at, refresh_expires_at, last_activity_at, is_active, created_at FROM public.user_sessions WHERE refresh_token = $1"
        )
        .bind(refresh_token)
        .fetch_optional(pool)
        .await?;
        Ok(session)
    }

    /// 更新会话令牌
    pub async fn update_session_tokens(
        pool: &PgPool,
        session_id: Uuid,
        new_session_token: &str,
        new_refresh_token: &str,
    ) -> Result<(),sqlx::Error> {
        sqlx::query!(
            r#"
            UPDATE public.user_sessions
            SET session_token = $1, refresh_token = $2, last_activity_at = $3
            WHERE id = $4
            "#,
            new_session_token,
            new_refresh_token,
            Utc::now(),
            session_id
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 注销会话（设置为非活跃状态）
    pub async fn logout_session(pool: &PgPool, session_token: &str) -> Result<(), sqlx::Error> {
        sqlx::query!(
            "UPDATE public.user_sessions SET is_active = false WHERE session_token = $1",
            session_token
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 注销用户的所有会话
    pub async fn logout_all_user_sessions(pool: &PgPool, user_id: Uuid) -> Result<(), sqlx::Error> {
        sqlx::query!(
            "UPDATE public.user_sessions SET is_active = false WHERE user_id = $1",
            user_id
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 清理过期的会话
    pub async fn cleanup_expired_sessions(pool: &PgPool) -> Result<u64> {
        let result = sqlx::query!(
            "UPDATE public.user_sessions SET is_active = false WHERE refresh_expires_at < $1 AND is_active = true",
            Utc::now()
        )
        .execute(pool)
        .await?;
        Ok(result.rows_affected())
    }

    /// 获取用户的活跃会话数量
    pub async fn get_active_session_count(pool: &PgPool, user_id: Uuid) -> Result<i64> {
        let count = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM public.user_sessions WHERE user_id = $1 AND is_active = true AND refresh_expires_at > $2",
            user_id,
            Utc::now()
        )
        .fetch_one(pool)
        .await
        .context("Failed to get active session count")?;

        Ok(count.unwrap_or(0))
    }

    /// 更新会话的最后活动时间
    pub async fn update_last_activity(pool: &PgPool, session_token: &str) -> Result<()> {
        sqlx::query!(
            "UPDATE public.user_sessions SET last_activity_at = $1 WHERE session_token = $2 AND is_active = true",
            Utc::now(),
            session_token
        )
        .execute(pool)
        .await
        .context("Failed to update session last activity")?;

        Ok(())
    }
}
