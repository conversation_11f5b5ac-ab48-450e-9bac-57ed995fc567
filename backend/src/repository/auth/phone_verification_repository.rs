use anyhow::{ Result};
use chrono::{DateTime, Utc};
use sqlx::PgPool;

use crate::model::user::auth::PhoneVerificationCode;

/// 手机验证码数据访问层
pub struct PhoneVerificationRepository;

impl PhoneVerificationRepository {
    /// 清理过期的验证码
    pub async fn cleanup_expired_codes(pool: &PgPool, phone: &str) -> Result<(), sqlx::Error> {
        sqlx::query!(
            "DELETE FROM public.phone_verification_codes WHERE phone_number = $1 AND expires_at < $2",
            phone,
            Utc::now()
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 获取有效的验证码
    pub async fn get_valid_code(
        pool: &PgPool,
        phone: &str,
        code_type: &str,
    ) -> Result<Option<PhoneVerificationCode>, sqlx::Error> {
        let code = sqlx::query_as::<_, PhoneVerificationCode>(
            r#"
            SELECT id, phone_number, verification_code, code_type, expires_at, attempts, max_attempts, verified, verified_at, created_at
            FROM public.phone_verification_codes
            WHERE phone_number = $1 AND code_type = $2 AND expires_at > $3 AND verified = false
            ORDER BY created_at DESC
            LIMIT 1
            "#,
        )
        .bind(phone)
        .bind(code_type)
        .bind(Utc::now())
        .fetch_optional(pool)
        .await?;
        Ok(code)
    }

    /// 保存新的验证码
    pub async fn save_verification_code(
        pool: &PgPool,
        phone: &str,
        code: &str,
        code_type: &str,
        expires_at: DateTime<Utc>,
    ) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            INSERT INTO public.phone_verification_codes
            (phone_number, verification_code, code_type, expires_at)
            VALUES ($1, $2, $3, $4)
            "#,
            phone,
            code,
            code_type,
            expires_at
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 验证验证码并增加尝试次数
    pub async fn verify_and_increment_attempts(
        pool: &PgPool,
        phone: &str,
        code: &str,
        code_type: &str,
    ) -> Result<Option<PhoneVerificationCode>, sqlx::Error> {
        let verification_code = sqlx::query_as::<_, PhoneVerificationCode>(
            r#"
            SELECT id, phone_number, verification_code, code_type, expires_at, attempts, max_attempts, verified, verified_at, created_at
            FROM public.phone_verification_codes
            WHERE phone_number = $1 AND verification_code = $2 AND code_type = $3 AND verified = false
            ORDER BY created_at DESC
            LIMIT 1
            "#,
        )
        .bind(phone)
        .bind(code)
        .bind(code_type)
        .fetch_optional(pool)
        .await?;
        if let Some(ref vc) = verification_code {
            // 增加尝试次数
            sqlx::query!(
                "UPDATE public.phone_verification_codes SET attempts = attempts + 1 WHERE id = $1",
                vc.id
            )
            .execute(pool)
            .await?;
        }
        Ok(verification_code)
    }

    /// 标记验证码为已验证
    pub async fn mark_code_as_verified(pool: &PgPool, phone: &str, code: &str) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            UPDATE public.phone_verification_codes
            SET verified = true, verified_at = $1
            WHERE phone_number = $2 AND verification_code = $3
            "#,
            Utc::now(),
            phone,
            code
        )
        .execute(pool)
        .await?;
        Ok(())
    }
}
