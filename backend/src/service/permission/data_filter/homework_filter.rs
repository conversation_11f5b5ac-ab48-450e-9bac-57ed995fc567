use anyhow::Result;
use async_trait::async_trait;
use sqlx::{<PERSON><PERSON><PERSON>, QueryBuilder};
use std::sync::Arc;
use tracing::{debug, info, warn};
use uuid::Uuid;

use super::base::{BaseDataFilter, DataFilter, FilterCondition, FilterContext};
use super::casbin_query::CasbinQueryHelper;
use crate::service::permission::casbin_service::{CasbinPermissionService, DataScope};
use crate::service::permission::hierarchy_resolver::PermissionHierarchyResolver;

/// 作业数据过滤器
/// 根据用户的教育角色和权限范围过滤作业数据
#[derive(Clone)]
pub struct HomeworkDataFilter {
    query_helper: <PERSON><PERSON>bin<PERSON>ueryHelper,
    hierarchy_resolver: Arc<PermissionHierarchyResolver>,
}

impl HomeworkDataFilter {
    pub fn new(query_helper: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>el<PERSON>, hierarchy_resolver: Arc<PermissionHierarchyResolver>) -> Self {
        Self { query_helper, hierarchy_resolver }
    }

    /// 获取查询助手的数据库连接池引用
    fn get_pool(&self) -> &sqlx::PgPool {
        self.query_helper.get_pool()
    }

    /// 获取用户的作业权限范围
    async fn get_user_homework_scopes(&self, context: &FilterContext) -> Result<Vec<DataScope>> {
        // 使用层级解析器获取完整的权限范围
        self.hierarchy_resolver.resolve_user_permissions(&context.user_id, &context.tenant_id, "homework").await
    }

    /// 构建作业过滤条件
    async fn build_homework_filter_conditions(&self, context: &FilterContext, homework_scopes: &[DataScope]) -> Result<Vec<String>> {
        let mut conditions = Vec::new();

        for scope in homework_scopes {
            match scope.scope_type.as_str() {
                "all" | "school" => {
                    // 学校级权限：可以访问所有作业
                    info!("User {} has school-level homework access", context.user_id);
                    return Ok(vec!["1=1".to_string()]);
                }
                "subject_group" => {
                    // 学科组权限：只能访问本学科组的作业
                    conditions.push(format!("h.subject_group_id = '{}'", scope.scope_value));
                }
                "teaching_class" => {
                    // 教学班权限：只能访问特定班级的作业
                    let condition = self.build_teaching_class_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                }
                "grade" => {
                    // 年级权限：可以访问该年级所有班级的作业
                    let condition = self.build_grade_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                }
                "administrative_class" => {
                    // 行政班权限：访问该行政班学生的作业
                    let condition = self.build_administrative_class_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                }
                "individual" => {
                    // 个人权限：只能访问自己相关的作业
                    let condition = self.build_individual_condition(context).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                }
                _ => {
                    warn!("Unknown scope type for homework filter: {}", scope.scope_type);
                }
            }
        }

        Ok(conditions)
    }

    /// 构建教学班相关的过滤条件
    async fn build_teaching_class_condition(&self, context: &FilterContext, class_id: &str) -> Result<Option<String>> {
        // 通过 student_teaching_classes 表关联教学班
        Ok(Some(format!(
            r#"EXISTS (
                SELECT 1 FROM {}.homework_students hs
                JOIN {}.student_teaching_classes stc ON hs.student_id = stc.student_id
                WHERE hs.homework_id = h.id AND stc.class_id = '{}'
            )"#,
            context.schema_name, context.schema_name, class_id
        )))
    }

    /// 构建年级相关的过滤条件
    async fn build_grade_condition(&self, context: &FilterContext, grade_code: &str) -> Result<Option<String>> {
        // 通过学生的行政班关联年级
        Ok(Some(format!(
            r#"EXISTS (
                SELECT 1 FROM {}.homework_students hs
                JOIN {}.students s ON hs.student_id = s.id
                JOIN {}.administrative_classes ac ON s.administrative_class_id = ac.id
                WHERE hs.homework_id = h.id AND ac.grade_level_code = '{}'
            )"#,
            context.schema_name, context.schema_name, context.schema_name, grade_code
        )))
    }

    /// 构建行政班相关的过滤条件
    async fn build_administrative_class_condition(&self, context: &FilterContext, class_id: &str) -> Result<Option<String>> {
        // 通过学生的行政班关联
        Ok(Some(format!(
            r#"EXISTS (
                SELECT 1 FROM {}.homework_students hs
                JOIN {}.students s ON hs.student_id = s.id
                WHERE hs.homework_id = h.id AND s.administrative_class_id = '{}'
            )"#,
            context.schema_name, context.schema_name, class_id
        )))
    }

    /// 构建个人相关的过滤条件
    async fn build_individual_condition(&self, context: &FilterContext) -> Result<Option<String>> {
        // 学生只能查看自己的作业
        if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
            return Ok(Some(format!(
                "EXISTS (SELECT 1 FROM {}.homework_students hs WHERE hs.homework_id = h.id AND hs.student_id = '{}')",
                context.schema_name, student_id
            )));
        }

        // 教师可以查看自己创建的作业或负责的作业
        let mut teacher_conditions = Vec::new();

        // 1. 自己创建的作业
        teacher_conditions.push(format!("h.created_by = '{}'", context.user_id));

        // 2. 自己负责的学科组作业
        let subject_groups = self.query_helper.get_user_managed_subject_groups(context).await?;
        if !subject_groups.is_empty() {
            let sg_ids: Vec<String> = subject_groups.iter().map(|id| format!("'{}'", id)).collect();
            teacher_conditions.push(format!("h.subject_group_id IN ({})", sg_ids.join(",")));
        }

        // 3. 自己教授的班级作业
        let teaching_classes = self.query_helper.get_user_teaching_classes(context).await?;
        if !teaching_classes.is_empty() {
            let class_ids: Vec<String> = teaching_classes.iter().map(|id| format!("'{}'", id)).collect();
            teacher_conditions.push(format!(
                "EXISTS (SELECT 1 FROM {}.homework_students hs WHERE hs.homework_id = h.id AND hs.class_id IN ({}))",
                context.schema_name,
                class_ids.join(",")
            ));
        }

        if !teacher_conditions.is_empty() {
            Ok(Some(format!("({})", teacher_conditions.join(" OR "))))
        } else {
            Ok(None)
        }
    }

    /// 检查用户是否有作业管理权限
    async fn has_homework_management_permission(&self, context: &FilterContext, casbin_service: &dyn CasbinPermissionService) -> Result<bool> {
        let user_identity = format!("user:{}", context.user_id);

        // 检查是否有作业管理权限
        casbin_service
            .enforce(&crate::service::permission::casbin_service::PermissionRequest {
                subject: user_identity,
                domain: context.tenant_id.clone(),
                object: "homework".to_string(),
                action: "manage".to_string(),
            })
            .await
    }

    /// 构建基于用户身份的直接过滤条件
    async fn build_identity_based_conditions(&self, context: &FilterContext) -> Result<Vec<String>> {
        let mut conditions = Vec::new();
        let user_identities = self.query_helper.get_user_identities(context).await?;

        for identity in user_identities {
            if identity.starts_with("teacher") {
                // 任课老师：查看自己教授班级的作业
                let teaching_classes = self.query_helper.get_user_teaching_classes(context).await?;
                if !teaching_classes.is_empty() {
                    let class_ids: Vec<String> = teaching_classes.iter().map(|id| format!("'{}'", id)).collect();
                    conditions.push(format!(
                        r#"EXISTS (
                            SELECT 1 FROM {}.homework_students hs
                            JOIN {}.student_teaching_classes stc ON hs.student_id = stc.student_id
                            WHERE hs.homework_id = h.id AND stc.class_id IN ({})
                        )"#,
                        context.schema_name,
                        context.schema_name,
                        class_ids.join(",")
                    ));
                }
            } else if identity.starts_with("class_teacher") {
                // 班主任：查看所管理班级学生的作业
                let managed_classes = self.query_helper.get_user_managed_classes(context).await?;
                if !managed_classes.is_empty() {
                    let class_ids: Vec<String> = managed_classes.iter().map(|id| format!("'{}'", id)).collect();
                    conditions.push(format!(
                        r#"EXISTS (
                            SELECT 1 FROM {}.homework_students hs
                            JOIN {}.students s ON hs.student_id = s.id
                            WHERE hs.homework_id = h.id AND s.administrative_class_id IN ({})
                        )"#,
                        context.schema_name,
                        context.schema_name,
                        class_ids.join(",")
                    ));
                }
            } else if identity.starts_with("subject_leader") {
                // 学科组长：查看本学科组的作业
                let subject_groups = self.query_helper.get_user_managed_subject_groups(context).await?;
                if !subject_groups.is_empty() {
                    let sg_ids: Vec<String> = subject_groups.iter().map(|id| format!("'{}'", id)).collect();
                    conditions.push(format!("h.subject_group_id IN ({})", sg_ids.join(",")));
                }
            } else if identity.starts_with("grade_leader") {
                // 年级长：查看本年级的作业
                let managed_grades = self.query_helper.get_user_managed_grades(context).await?;
                for grade_id in managed_grades {
                    conditions.push(format!(
                        r#"EXISTS (
                            SELECT 1 FROM {}.homework_students hs
                            JOIN {}.students s ON hs.student_id = s.id
                            JOIN {}.administrative_classes ac ON s.administrative_class_id = ac.id
                            WHERE hs.homework_id = h.id AND ac.grade_level_code = '{}'
                        )"#,
                        context.schema_name, context.schema_name, context.schema_name, grade_id
                    ));
                }
            } else if identity.starts_with("student") {
                // 学生：只能查看自己的作业
                if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.homework_students hs WHERE hs.homework_id = h.id AND hs.student_id = '{}')",
                        context.schema_name, student_id
                    ));
                }
            }
        }

        Ok(conditions)
    }
}

#[async_trait]
impl DataFilter for HomeworkDataFilter {
    /// 获取过滤条件
    async fn get_filter_condition(&self, context: &FilterContext, casbin_service: &dyn CasbinPermissionService) -> Result<Option<FilterCondition>> {
        // 使用默认表别名
        self.get_filter_condition_with_alias(context, casbin_service, "h").await
    }

    async fn get_filter_condition_with_alias(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
        _table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        // 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            return Ok(None);
        }

        // 检查是否有作业管理权限
        if self.has_homework_management_permission(context, casbin_service).await? {
            return Ok(None);
        }

        // 获取用户的作业权限范围
        let homework_scopes = self.get_user_homework_scopes(context).await?;

        // 构建过滤条件
        let conditions = if !homework_scopes.is_empty() {
            self.build_homework_filter_conditions(context, &homework_scopes).await?
        } else {
            // 如果没有通过权限范围获取到条件，尝试基于身份构建
            self.build_identity_based_conditions(context).await?
        };

        if conditions.is_empty() {
            // 无权限，返回空结果条件
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 合并多个条件（OR关系）
        let where_clauses: Vec<String> = conditions.iter()
            .map(|c| format!("({})", c))
            .collect();
        let where_clause = where_clauses.join(" OR ");

        Ok(Some(FilterCondition {
            where_clause,
            parameters: vec![], // 这些方法返回的是字符串条件，不包含参数
        }))
    }

    /// 应用过滤条件到查询构建器
    fn apply_filter_to_query<'a>(&self, query_builder: &mut QueryBuilder<'a, Postgres>, condition: &'a FilterCondition) -> Result<()> {
        query_builder.push(" AND (");
        query_builder.push(&condition.where_clause);
        query_builder.push(")");
        Ok(())
    }

    /// 应用过滤条件到计数查询构建器
    fn apply_filter_to_count_query<'a>(&self, count_builder: &mut QueryBuilder<'a, Postgres>, condition: &'a FilterCondition) -> Result<()> {
        count_builder.push(" AND (");
        count_builder.push(&condition.where_clause);
        count_builder.push(")");
        Ok(())
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        info!("Applying homework data filter for user {} in tenant {}", context.user_id, context.tenant_id);

        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            info!("User {} is system admin, no homework filtering applied", context.user_id);
            return Ok(false);
        }

        // 2. 检查是否有作业管理权限
        if self.has_homework_management_permission(context, casbin_service).await? {
            info!("User {} has homework management permission, no filtering applied", context.user_id);
            return Ok(false);
        }

        // 3. 获取用户的作业权限范围
        let homework_scopes = self.get_user_homework_scopes(context).await?;

        // 4. 构建过滤条件
        let mut conditions = if !homework_scopes.is_empty() {
            self.build_homework_filter_conditions(context, &homework_scopes).await?
        } else {
            // 如果没有通过权限范围获取到条件，尝试基于身份构建
            self.build_identity_based_conditions(context).await?
        };

        if conditions.is_empty() {
            warn!("User {} has no homework access permissions", context.user_id);
            // 无权限，返回空结果
            query_builder.push(" AND 1=0");
            count_builder.push(" AND 1=0");
            return Ok(true);
        }

        // 5. 应用过滤条件
        let filter_condition = if conditions.len() == 1 && conditions[0] == "1=1" {
            // 全权限，不需要过滤
            return Ok(false);
        } else {
            format!(" AND ({})", conditions.join(" OR "))
        };

        debug!("Applying homework filter condition: {}", filter_condition);
        query_builder.push(&filter_condition);
        count_builder.push(&filter_condition);

        info!("Successfully applied homework data filter for user {}", context.user_id);
        Ok(true)
    }

    async fn apply_filter_to_query_builders<'a>(&self, context: &FilterContext, query_builder: &mut QueryBuilder<'a, Postgres>, casbin_service: &dyn CasbinPermissionService, table_alias: &str) -> Result<bool> {
        todo!()
    }
}
