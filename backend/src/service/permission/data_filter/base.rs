use anyhow::Result;
use async_trait::async_trait;
use sqlx::{QueryBuilder, Postgres};
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use tracing::{debug, warn};
use super::super::{CasbinPermissionService, DataScope};

/// 数据过滤条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterCondition {
    /// SQL WHERE 条件
    pub where_clause: String,
    /// 绑定参数
    pub parameters: Vec<FilterParam>,
}

/// 过滤参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterParam {
    Uuid(Uuid),
    String(String),
    Int(i64),
    Bool(bool),
}

/// 数据过滤上下文
#[derive(Debug, Clone)]
pub struct FilterContext {
    /// 用户ID
    pub user_id: Uuid,
    /// 租户ID
    pub tenant_id: String,
    /// 用户身份标识（用于Casbin）
    pub user_identity: String,
    /// 资源类型
    pub resource: String,
    /// 操作类型
    pub action: String,
    /// 数据库schema名称
    pub schema_name: String,
}

/// 通用数据过滤器trait
#[async_trait]
pub trait DataFilter: Send + Sync {
    /// 获取过滤条件
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>>;
    
    /// 应用过滤条件到查询构建器
    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()>;

    /// 应用过滤条件到计数查询构建器
    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()>;

    /// 直接应用过滤条件到查询构建器（新方法）
    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool>;

    async fn apply_filter_to_query_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool>;
    /// 获取带表别名的过滤条件
    async fn get_filter_condition_with_alias(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>>;
}

/// 基础数据过滤器实现
pub struct BaseDataFilter;

impl BaseDataFilter {
    /// 解析数据范围为过滤条件
    pub fn parse_data_scopes_to_condition(
        data_scopes: &[DataScope],
        resource: &str,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        if data_scopes.is_empty() {
            return Ok(None);
        }
        
        let mut conditions = Vec::new();
        let mut bind_params = Vec::new();
        
        for scope in data_scopes {
            if scope.resource != resource {
                continue;
            }
            
            match scope.scope_type.as_str() {
                "class" => {
                    if scope.scope_value != "*" {
                        if let Ok(class_id) = Uuid::parse_str(&scope.scope_value) {
                            conditions.push(format!("{}.administrative_class_id = ${}", 
                                                   table_alias, bind_params.len() + 1));
                            bind_params.push(FilterParam::Uuid(class_id));
                        }
                    }
                },
                "grade" => {
                    if scope.scope_value != "*" {
                        conditions.push(format!("{}.grade_level_code = ${}", 
                                               table_alias, bind_params.len() + 1));
                        bind_params.push(FilterParam::String(scope.scope_value.clone()));
                    }
                },
                "subject_group" => {
                    if scope.scope_value != "*" {
                        // 需要通过教师表关联查询
                        warn!("Subject group filtering not implemented yet");
                    }
                },
                "school" => {
                    // 学校级别权限，通常不需要额外过滤（租户隔离已处理）
                    debug!("School level permission detected, no additional filtering needed");
                },
                _ => {
                    warn!("Unknown scope type: {}", scope.scope_type);
                }
            }
        }
        
        if conditions.is_empty() {
            return Ok(None);
        }
        
        let sql_condition = if conditions.len() == 1 {
            conditions[0].clone()
        } else {
            format!("({})", conditions.join(" OR "))
        };
        
        Ok(Some(FilterCondition {
            where_clause: sql_condition,
            parameters: bind_params,
        }))
    }
    
    /// 检查用户是否为系统管理员或校长
    pub async fn is_system_admin(
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        use crate::service::permission::PermissionRequest;
        
        // 检查系统管理员权限
        let admin_request = PermissionRequest {
            subject: context.user_identity.clone(),
            domain: context.tenant_id.clone(),
            object: "system:admin".to_string(),
            action: "manage".to_string(),
        };
        
        if casbin_service.enforce(&admin_request).await? {
            return Ok(true);
        }
        
        // 检查是否为校长角色（校长应该有全校数据访问权限）
        let principal_request = PermissionRequest {
            subject: context.user_identity.clone(),
            domain: context.schema_name.clone(),
            object: format!("{}:school", context.resource),
            action: context.action.clone(),
        };
        
        casbin_service.enforce(&principal_request).await
    }
}

/// 变量定义解析结果
#[derive(Debug, Clone)]
pub struct VariableDefinition {
    pub resource: String,           // 资源类型：student, teacher, class等
    pub scope_type: String,         // 范围类型：school, grade, class, self等
    pub variable_name: String,      // 变量名：managed_classes, managed_grades等
    pub filter_field: String,       // 过滤字段：administrative_class_id, grade_id等
}