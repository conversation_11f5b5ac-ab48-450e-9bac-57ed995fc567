use anyhow::{Result, anyhow};
use async_trait::async_trait;
use sqlx::{Pg<PERSON><PERSON>, QueryBuilder, Postgres};
use uuid::Uuid;
use tracing::{info, warn};
use super::base::*;
use crate::model::permission::casbin_policy::CasbinPolicyRecord;
use super::casbin_query::CasbinQueryHelper;
use super::super::{CasbinPermissionService};

/// 基于Casbin策略的数据过滤器
/// 通过解析casbin_policies表中的策略，动态生成过滤条件
#[derive(Debug, Clone)]
pub struct CasbinBasedDataFilter {
    query_helper: CasbinQueryHelper,
}

impl CasbinBasedDataFilter {
    pub fn new(pool: PgPool) -> Self {
        Self {
            query_helper: CasbinQueryHelper::new(pool),
        }
    }

    /// 从casbin_policies获取基于策略的过滤条件
    async fn get_filter_from_casbin_policies(
        &self,
        context: &FilterContext,
        casbin_service: &dyn <PERSON><PERSON>binPermissionService,
    ) -> Result<Option<FilterCondition>> {
        info!("CasbinBasedDataFilter: Processing user_id={}, resource={}", 
              context.user_id, context.resource);

        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            info!("User {} is system admin, no data filtering applied", context.user_id);
            return Ok(None);
        }

        // 2. 获取用户身份和角色
        let user_identities = self.query_helper.get_user_identities(context).await?;
        if user_identities.is_empty() {
            warn!("No identities found for user: {}", context.user_id);
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }
        info!("Identities: {:?}", user_identities);
        // 3. 查询匹配的Casbin策略
        let policies = self.query_helper.query_casbin_policies(
            &user_identities,
            &context.schema_name,
            &context.resource,
            &context.action,
        ).await?;

        if policies.is_empty() {
            warn!("No matching policies found for user: {}, resource: {}", 
                  context.user_id, context.resource);
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 4. 解析策略并生成过滤条件
        let condition =self.generate_condition_from_policies(&policies, context).await;
        info!("Generated condition: {:?}", condition);
        condition
    }



    /// 从策略生成过滤条件
    async fn generate_condition_from_policies(
        &self,
        policies: &[CasbinPolicyRecord],
        context: &FilterContext,
    ) -> Result<Option<FilterCondition>> {
        let mut all_conditions = Vec::new();
        let mut all_parameters = Vec::new();

        for policy in policies {
            if let Some(condition) = self.parse_policy_to_condition(policy, context, "main").await? {
                all_conditions.push(condition.where_clause);
                all_parameters.extend(condition.parameters);
            }
        }

        if all_conditions.is_empty() {
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        let sql_condition = if all_conditions.len() == 1 {
            all_conditions[0].clone()
        } else {
            format!("({})", all_conditions.join(" OR "))
        };

        Ok(Some(FilterCondition {
            where_clause: sql_condition,
            parameters: all_parameters,
        }))
    }

    /// 解析单个策略为过滤条件
    async fn parse_policy_to_condition(
        &self,
        policy: &CasbinPolicyRecord,
        context: &FilterContext,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        let v2_str = policy.v2.as_deref().unwrap_or("");

        // 解析v2字段中的资源定义
        let variable_def = self.parse_resource_definition(v2_str)?;

        // 否则根据变量定义生成条件
        self.generate_condition_from_variable(&variable_def, context, table_alias).await
    }

    /// 解析资源定义
    /// 支持的格式：
    /// - student:school -> 全校学生
    /// - student:grade:managed_grades -> 管理的年级学生
    /// - student:class:managed_classes -> 管理的班级学生
    /// - student:self -> 自己的学生记录
    fn parse_resource_definition(&self, v2: &str) -> Result<VariableDefinition> {
        let parts: Vec<&str> = v2.split(':').collect();

        if parts.len() < 2 {
            return Err(anyhow!("Invalid resource definition format: {}", v2));
        }

        let resource = parts[0].to_string();
        let scope_type = parts[1].to_string();

        let (variable_name, filter_field) = match (resource.as_str(), scope_type.as_str()) {
            ("student", "school") => ("all".to_string(), "1".to_string()),
            ("student", "grade") => {
                let var_name = if parts.len() > 2 { parts[2].to_string() } else { "managed_grades".to_string() };
                (var_name, "grade_id".to_string())
            },
            ("student", "class") => {
                let var_name = if parts.len() > 2 { parts[2].to_string() } else { "managed_classes".to_string() };
                (var_name, "administrative_class_id".to_string())
            },
            ("student", "self") => ("user_student_id".to_string(), "id".to_string()),
            ("administrative_class", "school") => ("all".to_string(), "1".to_string()),
            ("administrative_class", "grade") => {
                let var_name = if parts.len() > 2 { parts[2].to_string() } else { "managed_grades".to_string() };
                (var_name, "grade_id".to_string())
            },
            ("administrative_class", "class") => {
                let var_name = if parts.len() > 2 { parts[2].to_string() } else { "managed_classes".to_string() };
                (var_name, "id".to_string())
            },
            ("teacher", "school") => ("all".to_string(), "1".to_string()),
            ("teacher", "subject_group") => {
                let var_name = if parts.len() > 2 { parts[2].to_string() } else { "managed_subject_groups".to_string() };
                (var_name, "subject_group_id".to_string())
            },
            (_, "self") => ("self_student_id".to_string(), "student_id".to_string()),
            (_, "subject_group") => {
                let var_name = if parts.len() > 2 { parts[2].to_string() } else { "managed_subject_groups".to_string() };
                (var_name, "subject_group_id".to_string())
            },
            (_, "school") => {
                ("all".to_string(), "1".to_string())
            },

            _ => return Err(anyhow!("Unsupported resource definition: {}", v2)),
        };

        Ok(VariableDefinition {
            resource,
            scope_type,
            variable_name,
            filter_field,
        })
    }

    /// 从Casbin策略获取过滤条件（带表别名）
    async fn get_filter_from_casbin_policies_with_alias(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        // 获取用户身份列表
        let user_identities = self.query_helper.get_user_identities(context).await?;
        
        if user_identities.is_empty() {
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 查询相关的Casbin策略
        let policies = self.query_helper.query_casbin_policies(
            &user_identities,
            &context.schema_name,
            &context.resource,
            &context.action,
        ).await?;

        if policies.is_empty() {
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 解析策略为过滤条件
        let mut conditions = Vec::new();
        for policy in &policies {
            if let Some(condition) = self.parse_policy_to_condition(policy, context, table_alias).await? {
                conditions.push(condition);
            }
        }

        if conditions.is_empty() {
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 合并多个条件（OR关系）
        let mut sql_conditions = Vec::new();
        let mut all_parameters = Vec::new();

        for condition in conditions {
            sql_conditions.push(format!("{}", condition.where_clause));
            all_parameters.extend(condition.parameters);
        }

        let sql_condition = sql_conditions.join(" OR ");

        Ok(Some(FilterCondition {
            where_clause: sql_condition,
            parameters: all_parameters,
        }))
    }

    /// 根据变量定义生成过滤条件
    async fn generate_condition_from_variable(
        &self,
        variable_def: &VariableDefinition,
        context: &FilterContext,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        
        match variable_def.variable_name.as_str() {
            "all" => {
                // 全局权限，无限制
                Ok(Some(FilterCondition {
                    where_clause: "1=1".to_string(),
                    parameters: vec![],
                }))
            },
            "managed_classes" => {
                // 管理的班级
                let class_ids = self.query_helper.get_user_managed_classes(context).await?;
                self.build_in_condition(&variable_def.filter_field, &class_ids, &table_alias)
            },
            "managed_grades" => {
                // 管理的年级
                let grade_codes = self.query_helper.get_user_managed_grades(context).await?;
                self.build_string_in_condition(&variable_def.filter_field, &grade_codes, &table_alias)
            },
            "managed_subject_groups" => {
                // 管理的学科组
                let subject_group_ids = self.query_helper.get_user_managed_subject_groups(context).await?;
                self.build_in_condition(&variable_def.filter_field, &subject_group_ids, &table_alias)
            },
            "user_student_id" => {
                // 用户对应的学生ID
                if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
                    Ok(Some(FilterCondition {
                        where_clause: format!("{}.{} = ?", table_alias, variable_def.filter_field),
                        parameters: vec![FilterParam::Uuid(student_id)],
                    }))
                } else {
                    Ok(Some(FilterCondition {
                        where_clause: "1=0".to_string(),
                        parameters: vec![],
                    }))
                }
            },
            "self_student_id" => {
                // 用户对应的学生ID
                if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
                    Ok(Some(FilterCondition {
                        where_clause: format!("{}.{} = ", table_alias, variable_def.filter_field),
                        parameters: vec![FilterParam::Uuid(student_id)],
                    }))
                } else {
                    Ok(Some(FilterCondition {
                        where_clause: "1=0".to_string(),
                        parameters: vec![],
                    }))
                }
            },
            "teaching_classes" => {
                // 教授的班级
                let teaching_class_ids = self.query_helper.get_user_teaching_classes(context).await?;
                self.build_in_condition(&variable_def.filter_field, &teaching_class_ids, &table_alias)
            },
            _ => {
                warn!("Unknown variable name: {}", variable_def.variable_name);
                Ok(Some(FilterCondition {
                    where_clause: "1=0".to_string(),
                    parameters: vec![],
                }))
            }
        }
    }

    /// 根据资源类型获取表别名
    fn get_table_alias_for_resource(&self, resource: &str) -> String {
        match resource {
            "student" => "s".to_string(),
            "teacher" => "t".to_string(),
            "homework" => "h".to_string(),
            "exam" => "e".to_string(),
            "class" => "c".to_string(),
            "subject_group" => "sg".to_string(),
            "grade" => "g".to_string(),
            _ => "main".to_string(), // 默认别名
        }
    }

    /// 构建IN条件
    fn build_in_condition(
        &self,
        field: &str,
        ids: &[Uuid],
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        if ids.is_empty() {
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 构建完整的IN条件，使用特殊标记让QueryBuilder知道这是一个IN条件
        let condition = format!("{}.{} IN", table_alias, field);

        let params = ids.iter()
            .map(|id| FilterParam::Uuid(*id))
            .collect();

        Ok(Some(FilterCondition {
            where_clause: condition,
            parameters: params,
        }))
    }

    /// 构建字符串IN条件
    fn build_string_in_condition(
        &self,
        field: &str,
        values: &[String],
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        if values.is_empty() {
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 构建完整的IN条件，使用特殊标记让QueryBuilder知道这是一个IN条件
        let condition = format!("{}.{} IN", table_alias, field);
        let params = values.iter().map(|v| crate::service::permission::data_filter::base::FilterParam::String(v.clone())).collect();

        Ok(Some(FilterCondition {
            where_clause: condition,
            parameters: params,
        }))
    }

    /// 清除缓存
    pub fn clear_cache(&self) {
        self.query_helper.clear_cache();
    }

    /// 清除特定用户的缓存
    pub fn clear_user_cache(&self, user_id: &Uuid, tenant_id: &str) {
        self.query_helper.clear_user_cache(user_id, tenant_id);
    }

    /// 验证策略格式
    fn validate_policy_format(policy: &CasbinPolicyRecord) -> Result<()> {
        if policy.ptype != "p" {
            return Err(anyhow!("Invalid policy type: {}", policy.ptype));
        }

        if policy.v0.is_none() {
            return Err(anyhow!("Missing subject in policy"));
        }

        if policy.v2.is_none() {
            return Err(anyhow!("Missing object in policy"));
        }

        Ok(())
    }
}

#[async_trait]
impl DataFilter for CasbinBasedDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 使用默认表别名，这个方法主要用于向后兼容
        self.get_filter_condition_with_alias(context, casbin_service, "main").await
    }

    /// 获取带表别名的过滤条件
    async fn get_filter_condition_with_alias(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        self.get_filter_from_casbin_policies_with_alias(context, casbin_service, table_alias).await
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        let condition = match self.get_filter_condition_with_alias(context, casbin_service, table_alias).await? {
            Some(condition) => condition,
            None => return Ok(false),
        };
        info!("Applying filter condition: {:?}", condition);
        // 克隆参数以避免生命周期问题
        let params = condition.parameters.clone();
        let where_clause = condition.where_clause.clone();

        // 特殊处理 IN 条件
        if where_clause.ends_with("IN") {
            if params.is_empty() {
                // 如果没有参数，应该使用 1=0 来表示无匹配
                query_builder.push(" AND 1=0");
                count_builder.push(" AND 1=0");
            } else {
                // 替换 PLACEHOLDER_LIST 为实际的参数列表
                query_builder.push(" AND ").push(&where_clause).push(" (");
                count_builder.push(" AND ").push(&where_clause).push(" (");
                let mut separated = query_builder.separated(", ");
                let mut count_separated = count_builder.separated(", ");
                for param in params.iter() {
                    match param {
                        FilterParam::String(s) => {
                            separated.push_bind(s.clone());
                            count_separated.push_bind(s.clone());
                        },
                        FilterParam::Uuid(u) => {
                            separated.push_bind(*u);
                            count_separated.push_bind(*u);
                        },
                        FilterParam::Int(i) => {
                            separated.push_bind(*i);
                            count_separated.push_bind(*i);
                        },
                        FilterParam::Bool(b) => {
                            separated.push_bind(*b);
                            count_separated.push_bind(*b);
                        },
                    }
                }
                query_builder.push(")");
                count_builder.push(")");

                info!("Replaced IN condition with actual parameters:{}", query_builder.sql())
            }
        } else {
            // 普通条件处理 - 应用过滤条件到查询构建器
            query_builder.push(" AND (");
            count_builder.push(" AND (");
            // 普通条件处理
            query_builder.push(&where_clause);
            count_builder.push(&where_clause);

            // 绑定参数
            for param in &params {
                match param {
                    FilterParam::String(s) => {
                        query_builder.push_bind(s.clone());
                        count_builder.push_bind(s.clone());
                    },
                    FilterParam::Uuid(u) => {
                        query_builder.push_bind(*u);
                        count_builder.push_bind(*u);
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(*i);
                        count_builder.push_bind(*i);
                    },
                    FilterParam::Bool(b) => {
                        query_builder.push_bind(*b);
                        count_builder.push_bind(*b);
                    },
                }
            }
            query_builder.push(")");
            count_builder.push(")");
        }
        info!("Applied filter query builder sql: {}", query_builder.sql());
        Ok(true)
    }

    async fn apply_filter_to_query_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        let condition = match self.get_filter_condition_with_alias(context, casbin_service, table_alias).await? {
            Some(condition) => condition,
            None => return Ok(false),
        };
        info!("Applying filter condition: {:?}", condition);
        // 克隆参数以避免生命周期问题
        let params = condition.parameters.clone();
        let where_clause = condition.where_clause.clone();

        // 特殊处理 IN 条件
        if where_clause.ends_with("IN") {
            if params.is_empty() {
                // 如果没有参数，应该使用 1=0 来表示无匹配
                query_builder.push(" AND 1=0");
            } else {
                // 替换 PLACEHOLDER_LIST 为实际的参数列表
                query_builder.push(" AND ").push(&where_clause).push(" (");
                let mut separated = query_builder.separated(", ");
                for param in params.iter() {
                    match param {
                        FilterParam::String(s) => {
                            separated.push_bind(s.clone());
                        },
                        FilterParam::Uuid(u) => {
                            separated.push_bind(*u);
                        },
                        FilterParam::Int(i) => {
                            separated.push_bind(*i);
                        },
                        FilterParam::Bool(b) => {
                            separated.push_bind(*b);
                        },
                    }
                }
                query_builder.push(")");
                info!("Replaced IN condition with actual parameters:{}", query_builder.sql())
            }
        } else {
            // 普通条件处理 - 应用过滤条件到查询构建器
            query_builder.push(" AND (");
            // 普通条件处理
            query_builder.push(&where_clause);

            // 绑定参数
            for param in &params {
                match param {
                    FilterParam::String(s) => {
                        query_builder.push_bind(s.clone());
                    },
                    FilterParam::Uuid(u) => {
                        query_builder.push_bind(*u);
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(*i);
                    },
                    FilterParam::Bool(b) => {
                        query_builder.push_bind(*b);
                    },
                }
            }
            query_builder.push(")");
        }
        info!("Applied filter query builder sql: {}", query_builder.sql());
        Ok(true)
    }

    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        query_builder.push(" AND (");

        // 特殊处理 IN 条件
        if condition.where_clause.contains("PLACEHOLDER_LIST") {
            if condition.parameters.is_empty() {
                // 如果没有参数，应该使用 1=0 来表示无匹配
                query_builder.push("1=0");
            } else {
                // 替换 PLACEHOLDER_LIST 为实际的参数列表
                let field_part = condition.where_clause.replace("(PLACEHOLDER_LIST)", "");
                query_builder.push(&field_part).push("(");

                for (i, param) in condition.parameters.iter().enumerate() {
                    if i > 0 {
                        query_builder.push(", ");
                    }
                    match param {
                        FilterParam::Uuid(uuid) => { query_builder.push_bind(*uuid); },
                        FilterParam::String(s) => { query_builder.push_bind(s); },
                        FilterParam::Int(i) => { query_builder.push_bind(*i); },
                        FilterParam::Bool(b) => { query_builder.push_bind(*b); },
                    }
                }

                query_builder.push(")");
            }
        } else {
            query_builder.push(&condition.where_clause);

            for param in &condition.parameters {
                match param {
                    FilterParam::Uuid(uuid) => { query_builder.push_bind(*uuid); },
                    FilterParam::String(s) => { query_builder.push_bind(s); },
                    FilterParam::Int(i) => { query_builder.push_bind(*i); },
                    FilterParam::Bool(b) => { query_builder.push_bind(*b); },
                }
            }
        }

        query_builder.push(")");
        Ok(())
    }

    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        count_builder.push(" AND (");

        // 特殊处理 IN 条件
        if condition.where_clause.contains("PLACEHOLDER_LIST") {
            if condition.parameters.is_empty() {
                // 如果没有参数，应该使用 1=0 来表示无匹配
                count_builder.push("1=0");
            } else {
                // 替换 PLACEHOLDER_LIST 为实际的参数列表
                let field_part = condition.where_clause.replace("(PLACEHOLDER_LIST)", "");
                count_builder.push(&field_part).push("(");

                for (i, param) in condition.parameters.iter().enumerate() {
                    if i > 0 {
                        count_builder.push(", ");
                    }
                    match param {
                        FilterParam::Uuid(uuid) => { count_builder.push_bind(*uuid); },
                        FilterParam::String(s) => { count_builder.push_bind(s); },
                        FilterParam::Int(i) => { count_builder.push_bind(*i); },
                        FilterParam::Bool(b) => { count_builder.push_bind(*b); },
                    }
                }

                count_builder.push(")");
            }
        } else {
            count_builder.push(&condition.where_clause);

            for param in &condition.parameters {
                match param {
                    FilterParam::Uuid(uuid) => { count_builder.push_bind(*uuid); },
                    FilterParam::String(s) => { count_builder.push_bind(s); },
                    FilterParam::Int(i) => { count_builder.push_bind(*i); },
                    FilterParam::Bool(b) => { count_builder.push_bind(*b); },
                }
            }
        }

        count_builder.push(")");
        Ok(())
    }
}