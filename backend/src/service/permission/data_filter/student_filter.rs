use std::sync::Arc;
use anyhow::Result;
use async_trait::async_trait;
use sqlx::{Postgres, QueryBuilder};
use tracing::{debug, info, warn};
use uuid::Uuid;

use super::base::{DataFilter, FilterContext, FilterCondition, BaseDataFilter};
use super::casbin_query::CasbinQueryHelper;
use crate::service::permission::casbin_service::{CasbinPermissionService, DataScope};
use crate::service::permission::hierarchy_resolver::PermissionHierarchyResolver;

/// 学生数据过滤器
/// 根据用户的教育角色和权限范围过滤学生数据
#[derive(Clone)]
pub struct StudentDataFilter {
    query_helper: C<PERSON>binQueryHelper,
    hierarchy_resolver: Arc<PermissionHierarchyResolver>,
}

impl StudentDataFilter {
    pub fn new(
        query_helper: <PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON>el<PERSON>,
        hierarchy_resolver: Arc<PermissionHierarchyResolver>,
    ) -> Self {
        Self {
            query_helper,
            hierarchy_resolver,
        }
    }

    /// 获取查询助手的数据库连接池引用
    fn get_pool(&self) -> &sqlx::PgPool {
        self.query_helper.get_pool()
    }

    /// 获取用户的学生权限范围
    async fn get_user_student_scopes(&self, context: &FilterContext) -> Result<Vec<DataScope>> {
        // 使用层级解析器获取完整的权限范围
        self.hierarchy_resolver
            .resolve_user_permissions(&context.user_id, &context.tenant_id, "student")
            .await
    }

    /// 构建学生过滤条件
    async fn build_student_filter_conditions(
        &self,
        context: &FilterContext,
        student_scopes: &[DataScope],
    ) -> Result<Vec<String>> {
        let mut conditions = Vec::new();

        for scope in student_scopes {
            match scope.scope_type.as_str() {
                "all" | "school" => {
                    // 学校级权限：可以访问所有学生
                    info!("User {} has school-level student access", context.user_id);
                    return Ok(vec!["1=1".to_string()]);
                },
                "subject_group" => {
                    // 学科组权限：只能访问本学科组教学班的学生
                    let condition = self.build_subject_group_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                },
                "teaching_class" => {
                    // 教学班权限：只能访问特定教学班的学生
                    let condition = self.build_teaching_class_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                },
                "grade_level" => {
                    // 年级权限：可以访问该年级所有学生
                    conditions.push(format!(
                        r#"EXISTS (
                            SELECT 1 FROM {}.administrative_classes ac
                            WHERE s.administrative_class_id = ac.id AND ac.grade_level_code = '{}'
                        )"#,
                        context.schema_name, scope.scope_value
                    ));
                },
                "administrative_class" => {
                    // 行政班权限：访问该行政班的学生
                    conditions.push(format!("s.administrative_class_id = '{}'", scope.scope_value));
                },
                "individual" => {
                    // 个人权限：只能访问自己相关的学生
                    let condition = self.build_individual_condition(context).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                },
                _ => {
                    warn!("Unknown scope type for student filter: {}", scope.scope_type);
                }
            }
        }

        Ok(conditions)
    }

    /// 构建学科组相关的过滤条件
    async fn build_subject_group_condition(
        &self,
        context: &FilterContext,
        subject_group_id: &str,
    ) -> Result<Option<String>> {
        // 通过学科组的教学班获取学生
        Ok(Some(format!(
            r#"EXISTS (
                SELECT 1 FROM {}.student_teaching_classes stc
                JOIN {}.teaching_classes tc ON stc.class_id = tc.id
                WHERE stc.student_id = s.id 
                AND tc.subject_group_id = '{}' 
                AND tc.is_active = true
            )"#,
            context.schema_name, context.schema_name, subject_group_id
        )))
    }

    /// 构建教学班相关的过滤条件
    async fn build_teaching_class_condition(
        &self,
        context: &FilterContext,
        class_id: &str,
    ) -> Result<Option<String>> {
        // 通过学生教学班关联表获取学生
        Ok(Some(format!(
            r#"EXISTS (
                SELECT 1 FROM {}.student_teaching_classes stc
                WHERE stc.student_id = s.id AND stc.class_id = '{}'
            )"#,
            context.schema_name, class_id
        )))
    }

    /// 构建个人相关的过滤条件
    async fn build_individual_condition(&self, context: &FilterContext) -> Result<Option<String>> {
        // 学生只能查看自己的信息
        if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
            return Ok(Some(format!("s.id = '{}'", student_id)));
        }

        // 家长可以查看子女的信息
        if let Some(children_ids) = self.query_helper.get_user_children_ids(context).await? {
            if !children_ids.is_empty() {
                let child_id_strings: Vec<String> = children_ids.iter()
                    .map(|id| format!("'{}'", id))
                    .collect();
                return Ok(Some(format!("s.id IN ({})", child_id_strings.join(","))));
            }
        }

        // 教师可以查看自己相关的学生
        let mut teacher_conditions = Vec::new();

        // 1. 自己教授的教学班学生
        let teaching_classes = self.query_helper.get_user_teaching_classes(context).await?;
        if !teaching_classes.is_empty() {
            let class_ids: Vec<String> = teaching_classes.iter()
                .map(|id| format!("'{}'", id))
                .collect();
            teacher_conditions.push(format!(
                r#"EXISTS (
                    SELECT 1 FROM {}.student_teaching_classes stc
                    WHERE stc.student_id = s.id AND stc.class_id IN ({})
                )"#,
                context.schema_name, class_ids.join(",")
            ));
        }

        // 2. 自己管理的行政班学生（班主任）
        let managed_classes = self.query_helper.get_user_managed_classes(context).await?;
        if !managed_classes.is_empty() {
            let class_ids: Vec<String> = managed_classes.iter()
                .map(|id| format!("'{}'", id))
                .collect();
            teacher_conditions.push(format!("s.administrative_class_id IN ({})", class_ids.join(",")));
        }

        // 3. 自己负责的学科组学生（学科组长）
        let subject_groups = self.query_helper.get_user_managed_subject_groups(context).await?;
        for sg_id in subject_groups {
            if let Some(condition) = self.build_subject_group_condition(context, &sg_id.to_string()).await? {
                teacher_conditions.push(condition);
            }
        }

        if !teacher_conditions.is_empty() {
            Ok(Some(format!("({})", teacher_conditions.join(" OR "))))
        } else {
            Ok(None)
        }
    }

    /// 检查用户是否有学生管理权限
    async fn has_student_management_permission(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        let user_identity = format!("user:{}", context.user_id);
        
        // 检查是否有学生管理权限
        casbin_service.enforce(&crate::service::permission::casbin_service::PermissionRequest {
            subject: user_identity,
            domain: context.tenant_id.clone(),
            object: "student".to_string(),
            action: "manage".to_string(),
        }).await
    }

    /// 构建基于用户身份的直接过滤条件
    async fn build_identity_based_conditions(&self, context: &FilterContext) -> Result<Vec<String>> {
        let mut conditions = Vec::new();
        let user_identities = self.query_helper.get_user_identities(context).await?;

        for identity in user_identities {
            if identity.starts_with("teacher") {
                // 任课老师：只能查看自己教授班级的学生
                let teaching_classes = self.query_helper.get_user_teaching_classes(context).await?;
                if !teaching_classes.is_empty() {
                    let class_ids: Vec<String> = teaching_classes.iter()
                        .map(|id| format!("'{}'", id))
                        .collect();
                    conditions.push(format!(
                        r#"EXISTS (
                            SELECT 1 FROM {}.student_teaching_classes stc
                            WHERE stc.student_id = s.id AND stc.class_id IN ({})
                        )"#,
                        context.schema_name, class_ids.join(",")
                    ));
                }
            } else if identity.starts_with("class_teacher") {
                // 班主任：可以查看所管理班级的学生
                let managed_classes = self.query_helper.get_user_managed_classes(context).await?;
                if !managed_classes.is_empty() {
                    let class_ids: Vec<String> = managed_classes.iter()
                        .map(|id| format!("'{}'", id))
                        .collect();
                    conditions.push(format!("s.administrative_class_id IN ({})", class_ids.join(",")));
                }
            } else if identity.starts_with("subject_leader") {
                // 学科组长：可以查看本学科组所有教学班的学生
                let subject_groups = self.query_helper.get_user_managed_subject_groups(context).await?;
                for sg_id in subject_groups {
                    if let Some(condition) = self.build_subject_group_condition(context, &sg_id.to_string()).await? {
                        conditions.push(condition);
                    }
                }
            } else if identity.starts_with("grade_leader") {
                // 年级长：可以查看本年级所有学生
                let managed_grades = self.query_helper.get_user_managed_grades(context).await?;
                for grade_id in managed_grades {
                    conditions.push(format!(
                        r#"EXISTS (
                            SELECT 1 FROM {}.administrative_classes ac
                            WHERE s.administrative_class_id = ac.id AND ac.grade_level_code = '{}'
                        )"#,
                        context.schema_name, grade_id
                    ));
                }
            } else if identity.starts_with("student") {
                // 学生：只能查看自己的信息
                if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
                    conditions.push(format!("s.id = '{}'", student_id));
                }
            } else if identity.starts_with("parent") {
                // 家长：只能查看子女的信息
                if let Some(children_ids) = self.query_helper.get_user_children_ids(context).await? {
                    if !children_ids.is_empty() {
                        let child_id_strings: Vec<String> = children_ids.iter()
                            .map(|id| format!("'{}'", id))
                            .collect();
                        conditions.push(format!("s.id IN ({})", child_id_strings.join(",")));
                    }
                }
            }
        }

        Ok(conditions)
    }
}

#[async_trait]
impl DataFilter for StudentDataFilter {
    /// 获取过滤条件
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 使用默认表别名
        self.get_filter_condition_with_alias(context, casbin_service, "s").await
    }

    async fn get_filter_condition_with_alias(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
        _table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        // 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            return Ok(None); // 不需要过滤
        }

        // 检查是否有学生管理权限
        if self.has_student_management_permission(context, casbin_service).await? {
            return Ok(None); // 不需要过滤
        }

        // 获取用户的学生权限范围
        let student_scopes = self.get_user_student_scopes(context).await?;

        // 构建过滤条件
        let mut conditions = if !student_scopes.is_empty() {
            self.build_student_filter_conditions(context, &student_scopes).await?
        } else {
            // 如果没有通过权限范围获取到条件，尝试基于身份构建
            self.build_identity_based_conditions(context).await?
        };

        if conditions.is_empty() {
            // 无权限，返回拒绝所有的条件
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 返回过滤条件
        Ok(Some(FilterCondition {
            where_clause: conditions.join(" OR "),
            parameters: vec![],
        }))
    }

    /// 应用过滤条件到查询构建器
    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        query_builder.push(" AND (");
        query_builder.push(&condition.where_clause);
        query_builder.push(")");
        Ok(())
    }

    /// 应用过滤条件到计数查询构建器
    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        count_builder.push(" AND (");
        count_builder.push(&condition.where_clause);
        count_builder.push(")");
        Ok(())
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        info!("Applying student data filter for user {} in tenant {}", 
              context.user_id, context.tenant_id);

        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            info!("User {} is system admin, no student filtering applied", context.user_id);
            return Ok(false);
        }

        // 2. 检查是否有学生管理权限
        if self.has_student_management_permission(context, casbin_service).await? {
            info!("User {} has student management permission, no filtering applied", context.user_id);
            return Ok(false);
        }

        // 3. 获取用户的学生权限范围
        let student_scopes = self.get_user_student_scopes(context).await?;
        
        // 4. 构建过滤条件
        let mut conditions = if !student_scopes.is_empty() {
            self.build_student_filter_conditions(context, &student_scopes).await?
        } else {
            // 如果没有通过权限范围获取到条件，尝试基于身份构建
            self.build_identity_based_conditions(context).await?
        };

        if conditions.is_empty() {
            warn!("User {} has no student access permissions", context.user_id);
            // 无权限，返回空结果
            query_builder.push(" AND 1=0");
            count_builder.push(" AND 1=0");
            return Ok(true);
        }

        // 5. 应用过滤条件
        let filter_condition = if conditions.len() == 1 && conditions[0] == "1=1" {
            // 全权限，不需要过滤
            return Ok(false);
        } else {
            format!(" AND ({})", conditions.join(" OR "))
        };

        debug!("Applying student filter condition: {}", filter_condition);
        query_builder.push(&filter_condition);
        count_builder.push(&filter_condition);

        info!("Successfully applied student data filter for user {}", context.user_id);
        Ok(true)
    }

    async fn apply_filter_to_query_builders<'a>(&self, context: &FilterContext, query_builder: &mut QueryBuilder<'a, Postgres>, casbin_service: &dyn CasbinPermissionService, table_alias: &str) -> Result<bool> {
        todo!()
    }
}