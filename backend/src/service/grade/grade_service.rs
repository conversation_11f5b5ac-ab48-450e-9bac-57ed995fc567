use anyhow::bail;
use anyhow::Result;
use crate::model::grade::grade::{
    GradeLevel, CreateGradeLevel, UpdateGradeLevel, GradeLevelVO, GradeLevelSummary,
    GradeLevelQueryParams, GradeLevelStatistics,
};
use crate::model::base::PageResult;
use crate::utils::error::AppError;
use crate::repository::grade::grade_level_repository::GradeLevelRepository;
use sqlx::PgPool;
use uuid::Uuid;

#[derive(Clone)]
pub struct GradeService {
    repository: GradeLevelRepository,
}

impl GradeService {
    pub fn new(db_pool: PgPool) -> Self {
        Self {
            repository: GradeLevelRepository::new(db_pool)
        }
    }

    pub async fn get_all_grades(&self) -> Result<Vec<GradeLevel>, AppError> {
        self.repository.get_all_grades().await
    }

    pub async fn get_grade_by_id(&self, id: Uuid) -> Result<Option<GradeLevelVO>, AppError> {
        self.repository.get_grade_by_id(id).await
    }

    pub async fn get_grade_by_code(&self, code: &str) -> Result<Option<GradeLevel>, AppError> {
        self.repository.get_grade_by_code(code).await
    }

    pub async fn get_grade_by_name(&self, name: &str) -> Result<Option<GradeLevel>, AppError> {
        self.repository.get_grade_by_name(name).await
    }

    pub async fn create_grade(&self, payload: CreateGradeLevel) -> Result<GradeLevel> {
        // Check if code already exists
        if let Some(_) = self.repository.get_grade_by_code(&payload.code).await? {
            bail!("年级编码已经存在");
        }

        self.repository.create_grade(payload).await
    }

    pub async fn update_grade(&self, id: Uuid, payload: UpdateGradeLevel) -> Result<GradeLevel> {
        self.repository.update_grade(id, payload).await
    }

    pub async fn delete_grade(&self, id: Uuid) -> Result<(), AppError> {
        self.repository.delete_grade(id).await
    }

    // New methods required by the controller

    /// 获取年级列表（支持分页和查询）
    pub async fn get_grades(&self, params: GradeLevelQueryParams) -> Result<PageResult<GradeLevelVO>, AppError> {
        self.repository.get_grades(params).await
    }

    /// 获取年级简要信息列表（用于下拉选择）
    pub async fn get_grade_summaries(&self, is_active: Option<bool>) -> Result<Vec<GradeLevelSummary>> {
        self.repository.get_grade_summaries(is_active).await
    }

    /// 获取年级统计信息
    pub async fn get_grade_statistics(&self) -> Result<GradeLevelStatistics> {
        self.repository.get_grade_statistics().await
    }

    /// 批量更新年级排序
    pub async fn update_grade_orders(&self, orders: Vec<(Uuid, i32)>) -> Result<(), AppError> {
        self.repository.update_grade_orders(orders).await
    }

    /// 检查年级代码是否可用
    pub async fn is_code_available(&self, code: &str, exclude_id: Option<Uuid>) -> Result<bool, AppError> {
        self.repository.is_code_available(code, exclude_id).await
    }
}