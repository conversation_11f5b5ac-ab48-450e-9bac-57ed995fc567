use chrono::{NaiveDate, Utc};
use serde_json;
use sqlx::{PgConnection, PgPool};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::error;
use uuid::Uuid;
use crate::model::CreateStudentTeachingClass;
use crate::model::subject_groups::subject_groups::CreateSubjectGroupsParams;
use crate::model::teacher::import::{TeacherImportRecord, TeacherImportResult};
use crate::model::teaching_classes::teaching_classes::CreateTeachingClassesParams;
use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;
use crate::service::grade::grade_service::GradeService;
use crate::service::role::role_service::RoleService;
use crate::service::student::student_service::StudentService;
use crate::service::subject::SubjectService;
use crate::service::subject_groups::subject_groups_service::SubjectGroupsService;
use crate::service::teacher::teacher_service::TeacherService;
use crate::service::teaching_classes::teaching_classes_service::TeachingClassesService;
use crate::service::tenant::tenant_service::TenantService;
use crate::service::tenant::user::tenant_user_service::TenantUserService;
use crate::service::user::user_service::{UserService, CreateUserRequest};
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use crate::controller::tenant::user::tenant_user_controller::AddUserToTenantRequest;
use crate::model::role::role::AssignRoleRequest;
use crate::model::teacher::teacher::{CreateTeacherParams, Teacher, UpdateTeacherParams};

/// 教师导入服务
#[derive(Clone)]
pub struct TeacherImportService {
    db_pool: PgPool,
    teacher_service: TeacherService,
    subject_service: SubjectService,
    grade_service: GradeService,
    subject_groups_service: Arc<SubjectGroupsService>,
    administrative_classes_service: Arc<AdministrativeClassesService>,
    teaching_classes_service: Arc<TeachingClassesService>,
    student_service: Arc<StudentService>,
    user_service: UserService,
    tenant_user_service: Arc<TenantUserService>,
    tenant_service: TenantService,
    role_service: RoleService,
}

impl TeacherImportService {
    pub fn new(
        db_pool: PgPool,
        teacher_service: TeacherService,
        subject_service: SubjectService,
        grade_service: GradeService,
        subject_groups_service: Arc<SubjectGroupsService>,
        administrative_classes_service: Arc<AdministrativeClassesService>,
        teaching_classes_service: Arc<TeachingClassesService>,
        student_service: Arc<StudentService>,
        user_service: UserService,
        tenant_user_service: Arc<TenantUserService>,
        tenant_service: TenantService,
        role_service: RoleService,
    ) -> Self {
        Self {
            db_pool,
            teacher_service,
            subject_service,
            grade_service,
            subject_groups_service,
            administrative_classes_service,
            teaching_classes_service,
            student_service,
            user_service,
            tenant_user_service,
            tenant_service,
            role_service,
        }
    }

    /// 教师批量导入功能
    pub async fn import_teachers(
        &self,
        schema_name: &str,
        records: Vec<TeacherImportRecord>,
        academic_year: Option<String>,
    ) -> Result<TeacherImportResult, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;

        let mut result = TeacherImportResult::new();

        // 获取现有学科组映射
        let mut subject_group_map = self.teacher_service.get_subject_group_map(&safe_schema).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;
        
        // 获取现有行政班映射
        let admin_class_map = self.teacher_service.get_admin_class_map(&safe_schema).await
            .map_err(|e| AppError::InternalServerError(e.to_string()))?;

        for (index, record) in records.iter().enumerate() {
            let row_num = index + 2; // Excel行号从2开始（第1行是表头）
            
            // 跳过空记录
            if record.name.trim().is_empty() {
                continue;
            }

            match self.process_teacher_import_record(
                &safe_schema,
                record,
                &mut subject_group_map,
                &admin_class_map,
                &academic_year,
                &mut conn,
            ).await {
                Ok(_teacher) => {
                    result.add_success();
                }
                Err(error) => {
                    let record_json = serde_json::to_value(record)
                        .unwrap_or_else(|_| serde_json::Value::String("序列化失败".to_string()));
                    result.add_error(row_num, error.to_string(), record_json);
                }
            }
        }

        Ok(result)
    }

    /// 处理单个教师导入记录
    async fn process_teacher_import_record(
        &self,
        schema_name: &str,
        record: &TeacherImportRecord,
        subject_group_map: &mut HashMap<String, Uuid>,
        admin_class_map: &HashMap<String, Uuid>,
        academic_year: &Option<String>,
        conn: &mut PgConnection,
    ) -> Result<Teacher, AppError> {
        // 验证必填字段
        self.validate_required_fields(record).map_err(|e| AppError::InternalServerError(e.to_string()))?;

        let phone = record.phone.as_ref().unwrap_or(&String::new()).clone();

        // 1. 检查用户是否已存在，如果不存在则创建
        let user_response = match self.user_service.get_user_by_phone(&phone).await {
            Ok(Some(existing_user)) => {
                // 用户已存在，直接使用
                existing_user
            }
            Ok(None) => {
                // 用户不存在，创建新用户
                let user_create_request = CreateUserRequest {
                    username: phone.clone(),
                    phone: phone.clone(),
                    password: "123456".to_string(), // 默认密码，后续可以修改
                };

                self.user_service.create_user(user_create_request).await
                    .map_err(|e| {
                        error!("创建用户失败：{}", e);
                        AppError::InternalServerError(format!("创建用户失败: {}", e))
                    })?
            }
            Err(e) => {
                error!("查询用户失败：{}", e);
                return Err(AppError::InternalServerError(format!("查询用户失败: {}", e)));
            }
        };

        // 2. 获取租户信息
        let tenant_response = self.tenant_service.get_tenant_by_schema(schema_name).await
            .map_err(|e| {
                error!("获取租户信息失败：{}", e);
                AppError::InternalServerError(format!("获取租户信息失败: {}", e))
            })?;

        // 3. 关联租户为成员
        let add_user_request = AddUserToTenantRequest {
            user_id: user_response.user_id,
            access_type: Some("member".to_string()),
            expires_at: None,
        };

        self.tenant_user_service.add_user_to_tenant(tenant_response.id, add_user_request).await
            .map_err(|e| {
                error!("添加用户到租户失败：{}", e);
                AppError::InternalServerError(format!("添加用户到租户失败: {}", e))
            })?;

        // 4. 检查或创建学科组
        // 从任教班级中提取第一个班级的年级信息
        let grade_name = if let Some(ref teaching_classes) = record.teaching_classes {
            let first_class_code = teaching_classes
                .split(',')
                .next()
                .map(|s| s.trim())
                .filter(|s| !s.is_empty());

            if let Some(class_code) = first_class_code {
                // 根据班级编号获取行政班信息，从中提取年级
                match self.administrative_classes_service
                    .find_active_class_by_code(&schema_name.to_string(), &class_code.to_string())
                    .await
                {
                    Ok(Some(admin_class)) => {
                        // 从行政班获取年级信息
                        if let Some(ref grade_level_code) = admin_class.grade_level_code {
                            match self.grade_service
                                .get_grade_by_code(grade_level_code)
                                .await
                            {
                                Ok(Some(grade_level)) => Some(grade_level.name),
                                _ => None,
                            }
                        } else {
                            None
                        }
                    }
                    _ => None,
                }
            } else {
                None
            }
        } else {
            None
        };

        let subject_group_id = self.ensure_subject_group_exists(
            schema_name,
            &record.subject,
            grade_name.as_deref(),
            subject_group_map,
        ).await?;

        // 5. 解析班主任信息
        let homeroom_class_id = self.parse_homeroom_info(
            &record.position.as_deref().unwrap_or(""),
            admin_class_map,
        ).map_err(|e| AppError::InternalServerError(e.to_string()))?;

        // 6. 检查教师是否已存在，如果不存在则创建
        let teacher = match self.teacher_service.get_teacher_by_phone(schema_name, &phone).await {
            Ok(Some(existing_teacher)) => {
                // 教师已存在，检查是否需要更新user_id
                if existing_teacher.user_id.is_none() || existing_teacher.user_id != Some(user_response.user_id) {
                    // 需要更新教师的user_id
                    let update_params = UpdateTeacherParams {
                        id: existing_teacher.id,
                        user_id: Some(user_response.user_id),
                        employee_id: Some(existing_teacher.employee_id.clone()),
                        teacher_name: Some(existing_teacher.teacher_name.clone()),
                        phone: existing_teacher.phone.clone(),
                        email: existing_teacher.email.clone(),
                        gender: existing_teacher.gender.clone(),
                        date_of_birth: existing_teacher.date_of_birth,
                        id_card_number: existing_teacher.id_card_number.clone(),
                        highest_education: existing_teacher.highest_education.clone(),
                        graduation_school: existing_teacher.graduation_school.clone(),
                        major: existing_teacher.major.clone(),
                        hire_date: existing_teacher.hire_date,
                        employment_status: Some(existing_teacher.employment_status.clone()),
                        title: existing_teacher.title.clone(),
                        teaching_subjects: None, // Teacher模型中没有这个字段
                        homeroom_class_id: None, // Teacher模型中没有这个字段
                        grade_level_id: None, // Teacher模型中没有这个字段
                        subject_group_id: None, // Teacher模型中没有这个字段
                        office_location: existing_teacher.office_location.clone(),
                        is_active: Some(existing_teacher.is_active),
                        bio: None, // Teacher模型中没有这个字段
                    };

                    self.teacher_service.update_teacher(schema_name, &None, &update_params).await
                        .map_err(|e| {
                            error!("更新教师记录失败：{}", e);
                            AppError::InternalServerError(e.to_string())
                        })?;

                    // 重新获取更新后的教师信息
                    self.teacher_service.get_teacher_by_phone(schema_name, &phone).await
                        .map_err(|e| AppError::InternalServerError(e.to_string()))?
                        .unwrap() // 这里应该不会为None，因为刚刚更新过
                } else {
                    // 教师已存在且user_id正确，直接使用
                    existing_teacher
                }
            }
            Ok(None) => {
                // 教师不存在，创建新教师
                let employee_id = self.generate_employee_id(schema_name, conn).await
                    .map_err(|e| AppError::InternalServerError(e.to_string()))?;

                let create_params = CreateTeacherParams {
                    user_id: Some(user_response.user_id),
                    employee_id,
                    teacher_name: record.name.clone(),
                    phone: record.phone.clone(),
                    email: None,
                    gender: Some("未知".to_string()),
                    date_of_birth: None,
                    id_card_number: None,
                    highest_education: None,
                    graduation_school: None,
                    major: None,
                    hire_date: Some(NaiveDate::from(Utc::now().naive_utc())),
                    employment_status: "在职".to_string(),
                    title: record.position.clone(),
                    teaching_subjects: None,
                    homeroom_class_id,
                    grade_level_id: None,
                    subject_group_id: None,
                    office_location: None,
                    is_active: true,
                    bio: None,
                };

                self.teacher_service.create_teacher(schema_name, &create_params).await
                    .map_err(|e| {
                        error!("创建教师记录失败：{}", e);
                        AppError::InternalServerError(e.to_string())
                    })?
            }
            Err(e) => {
                error!("查询教师失败：{}", e);
                return Err(AppError::InternalServerError(format!("查询教师失败: {}", e)));
            }
        };

        // 8. 根据职位分配角色
        let position = record.position.as_deref().unwrap_or("");
        let role_codes = self.map_position_to_role_codes(position);

        for role_code in role_codes {
            // 根据角色代码获取角色信息
            match self.role_service.get_role_by_code(&role_code).await {
                Ok(role_vo) => {
                    let assign_request = AssignRoleRequest {
                        user_id: user_response.user_id,
                        tenant_id: tenant_response.id,
                        role_id: role_vo.id,
                        target_type: None, // 根据需求，不写入target_type
                        target_id: None,   // 根据需求，不写入target_id
                        subject: None,
                        display_name: None,
                    };

                    if let Err(e) = self.role_service.assign_role(assign_request, schema_name).await {
                        error!("分配角色 {} 失败：{}", role_code, e);
                        // 继续处理其他角色，不中断整个流程
                    }
                }
                Err(e) => {
                    error!("获取角色 {} 失败：{}", role_code, e);
                    // 继续处理其他角色，不中断整个流程
                }
            }
        }

        // 9. 创建教学班
        if let Some(ref teaching_classes) = record.teaching_classes {
            if !teaching_classes.trim().is_empty() {
                self.create_teaching_classes_for_teacher(
                    schema_name,
                    &teacher,
                    teaching_classes,
                    &subject_group_id,
                    academic_year,
                    record.subject.as_str(),
                ).await.map_err(|e| AppError::InternalServerError(e.to_string()))?;
            }
        }

        Ok(teacher)
    }

    /// 验证必填字段
    fn validate_required_fields(&self, record: &TeacherImportRecord) -> Result<(), String> {
        if record.name.trim().is_empty() {
            return Err("姓名不能为空".to_string());
        }
        if record.subject.trim().is_empty() {
            return Err("科目不能为空".to_string());
        }
        let empty_string = String::new();
        let phone = record.phone.as_ref().unwrap_or(&empty_string);
        if phone.trim().is_empty() {
            return Err("手机号不能为空".to_string());
        }

        // 验证手机号格式
        if !self.is_valid_phone(phone) {
            return Err("手机号格式不正确".to_string());
        }

        Ok(())
    }

    /// 验证手机号格式
    fn is_valid_phone(&self, phone: &str) -> bool {
        phone.len() == 11 && phone.chars().all(|c| c.is_ascii_digit())
    }

    /// 确保学科组存在，如果不存在则创建
    async fn ensure_subject_group_exists(
        &self,
        schema_name: &str,
        subject_name: &str,
        grade_name: &str,
        subject_group_map: &mut HashMap<String, Uuid>,
    ) -> Result<Uuid, AppError> {
        // 创建复合键：学科名称 + 年级名称
        let composite_key = format!("{}_{}", subject_name, grade_name);
        if let Some(&existing_id) = subject_group_map.get(&composite_key) {
            return Ok(existing_id);
        }

        // 获取学科代码
        let subject_code = self.map_subject_name_to_code(subject_name).await?.unwrap_or_default();

        // 获取年级代码
        let grade_level_code = self.map_grade_name_to_code(grade_name).await?.unwrap_or_default();

        // 创建新的学科组，名称包含年级信息
        let group_name = format!("{}{}学科组", grade_name, subject_name);
        let create_params = CreateSubjectGroupsParams {
            group_name,
            subject_code,
            grade_level_code: Some(grade_level_code),
            description: Some(format!("{}{}学科教学组", grade_name, subject_name)),
            leader_user_id: None,
        };

        let new_subject_group = self.subject_groups_service
            .create_subject_groups(&schema_name.to_string(), &create_params)
            .await
            .map_err(|e| AppError::InternalServerError(format!("创建学科组失败: {}", e)))?;

        let new_id = new_subject_group.id;
        subject_group_map.insert(composite_key, new_id);
        Ok(new_id)
    }

    /// 解析班主任信息
    fn parse_homeroom_info(
        &self,
        position: &str,
        admin_class_map: &HashMap<String, Uuid>,
    ) -> Result<Option<i64>, String> {
        if position.contains("班主任") {
            // 提取班级编码，格式如：班主任（704）
            if let Some(start) = position.find('（') {
                if let Some(end) = position.find('）') {
                    let class_code = &position[start + 3..end]; // 3是"（"的字节长度
                    return if let Some(&class_id) = admin_class_map.get(class_code) {
                        Ok(Some(class_id.as_u128() as i64))
                    } else {
                        Err(format!("未找到班级编码为 {} 的行政班", class_code))
                    }
                }
            }
        }
        Ok(None)
    }

    /// 生成教师工号
    async fn generate_employee_id(
        &self,
        schema_name: &str,
        conn: &mut PgConnection,
    ) -> Result<String, String> {
        let sql = format!(
            "SELECT COUNT(*) FROM {}.teachers",
            schema_name
        );
        
        let count: i64 = sqlx::query_scalar(&sql)
            .fetch_one(conn)
            .await
            .map_err(|e| format!("查询教师数量失败: {}", e))?;

        Ok(format!("T{:06}", count + 1))
    }

    /// 创建教学班
    async fn create_teaching_classes_for_teacher(
        &self,
        schema_name: &str,
        teacher: &Teacher,
        teaching_classes_str: &str,
        subject_group_id: &Uuid,
        academic_year: &Option<String>,
        subject_name: &str
    ) -> Result<(), String> {
        let class_codes: Vec<&str> = teaching_classes_str
            .split(',')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .collect();

        for class_code in class_codes {
            let class_name = format!("{}-{}", subject_name, class_code);
            
            // 复用TeachingClassesService创建教学班
            let create_params = CreateTeachingClassesParams {
                class_name,
                code: Some(class_code.to_string()),
                academic_year: Some(academic_year.as_ref().unwrap_or(&"2024-2025".to_string()).clone()),
                subject_group_id: Some(*subject_group_id),
                teacher_id: Some(teacher.id),
            };

            let teaching_class = self.teaching_classes_service
                .create_classes(&schema_name.to_string(), &create_params)
                .await
                .map_err(|e| format!("创建教学班失败: {}", e))?;

            // 根据班级编号找到对应的行政班，并创建学生与教学班关联
            self.create_student_teaching_class_associations(
                schema_name,
                class_code,
                &teaching_class.id,
            ).await.map_err(|e| {
                error!("创建学生教学班关联失败: {}", e);
                format!("创建学生教学班关联失败: {}", e)
            })?;
        }

        Ok(())
    }

    /// 根据班级编号创建学生与教学班关联
    async fn create_student_teaching_class_associations(
        &self,
        schema_name: &str,
        class_code: &str,
        teaching_class_id: &Uuid,
    ) -> Result<(), String> {
        // 1. 使用AdministrativeClassesService根据班级编号找到对应的行政班
        let admin_class = self.administrative_classes_service
            .find_active_class_by_code(&schema_name.to_string(), &class_code.to_string())
            .await?;
        
        if let Some(admin_class) = admin_class {
            // 2. 使用AdministrativeClassesService获取该行政班的所有学生
            let find_params = crate::model::administrative_classes::administrative_classes::FindAllStudentInClassParams {
                class_id: admin_class.id,
            };
            let students = self.administrative_classes_service
                .find_all_student_in_class(&schema_name.to_string(), &find_params)
                .await?;
            
            // 3. 为每个学生创建与教学班的关联
            for student in students {
                let create_association = CreateStudentTeachingClass {
                    student_id: student.id,
                    class_id: *teaching_class_id,
                };
                // 使用StudentService创建关联
                if let Err(e) = self.student_service
                    .add_teaching_class(schema_name, create_association)
                    .await
                {
                    error!("为学生 {} 创建教学班关联失败: {}", student.student_name, e);
                    // 继续处理其他学生，不中断整个流程
                }
            }
        } else {
            error!("未找到班级编号为 {} 的行政班", class_code);
        }
        
        Ok(())
    }

    /// 使用SubjectGroupsService根据学科组ID获取学科名称
    async fn get_subject_name_from_subject_group(
        &self,
        schema_name: &str,
        subject_group_id: &Uuid,
    ) -> Result<String, String> {
        let subject_group = self.subject_groups_service
            .as_ref()
            .get_subject_group_by_id(&schema_name.to_string(), subject_group_id)
            .await?;
            
        // 如果找到学科组，则使用学科代码作为学科名称；否则使用默认值
        Ok(subject_group
            .map(|sg| sg.subject_code)
            .unwrap_or_else(|| "未知学科".to_string()))
    }

    /// 学科名称到代码的映射（复用SubjectService）
    async fn map_subject_name_to_code(&self, subject_name: &str) -> Result<Option<String>, AppError> {
       self.subject_service.get_subject_by_name(subject_name).await?
            .map(|subject| Some(subject.code))
            .ok_or(AppError::NotFound(format!("未找到学科: {}", subject_name)))
    }

    /// 年级名称到代码的映射（复用GradeService）
    async fn map_grade_name_to_code(&self, grade_name: &str) -> Result<Option<String>, AppError> {
        let grade_name = match grade_name {
            "初一" => "七年级",
            "初二" => "八年级",
            "初三" => "九年级",
            "高一" => "十年级",
            "高二" => "十一年级",
            "高三" => "十二年级",
            _ => grade_name,
        };
        self.grade_service.get_grade_by_name(grade_name).await?
            .map(|grade| Some(grade.code))
            .ok_or(AppError::NotFound(format!("未找到年级: {}", grade_name)))
    }

    /// 根据职位映射到角色代码列表
    fn map_position_to_role_codes(&self, position: &str) -> Vec<String> {
        let mut role_codes = Vec::new();

        // 根据职位内容判断角色
        if position.contains("校长") {
            role_codes.push("principal".to_string());
        } else if position.contains("教导主任") {
            role_codes.push("academic_director".to_string());
        } else if position.contains("学科组长") {
            role_codes.push("subject_leader".to_string());
        } else if position.contains("年级长") {
            role_codes.push("grade_leader".to_string());
        } else if position.contains("班主任") {
            role_codes.push("class_teacher".to_string());
        }

        // 所有教师都有任课老师角色
        role_codes.push("teacher".to_string());

        role_codes
    }

}
