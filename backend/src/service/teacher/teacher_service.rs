use crate::model::base::PageResult;
use crate::model::subject_groups::subject_groups::{CreateSubjectGroupsParams};
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use sqlx::{PgPool, Row};
use uuid::Uuid;
use std::collections::HashMap;
use tracing::info;
use crate::model::teacher::teacher::{CreateTeacherParams, FindAllParams, PageAllTeacherParams, Teacher, TeacherDetailVO, TeacherListVO, TeacherQueryParams, TeacherSummary, UpdateTeacherParams};

#[derive(Clone)]
pub struct TeacherService {
    db_pool: PgPool,
}

impl TeacherService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 获取教师列表（分页）
    pub async fn get_teachers(
        &self,
        schema_name: &str,
        params: <PERSON><PERSON>ueryParams,
    ) -> Result<PageResult<TeacherListVO>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        // 构建基础查询
        let mut query = String::from(
            "SELECT 
                t.id,
                t.employee_id,
                t.teacher_name,
                t.phone,
                t.employment_status,
                t.title,
                t.teaching_subjects,
                t.is_active,
                c.name as homeroom_class_name,
                g.name as grade_level_name
            FROM teachers t
            LEFT JOIN classes c ON t.homeroom_class_id = c.id
            LEFT JOIN public.grade_levels g ON t.grade_level_id = g.id
            WHERE 1=1",
        );

        // 添加过滤条件
        if let Some(name) = &params.name {
            query.push_str(" AND t.teacher_name ILIKE '%");
            query.push_str(name);
            query.push_str("%'");
        }

        if let Some(employee_id) = &params.employee_id {
            query.push_str(" AND t.employee_id = '");
            query.push_str(employee_id);
            query.push_str("'");
        }

        if let Some(employment_status) = &params.employment_status {
            query.push_str(" AND t.employment_status = '");
            query.push_str(employment_status);
            query.push_str("'");
        }

        if let Some(is_active) = params.is_active {
            query.push_str(&format!(" AND t.is_active = {}", is_active));
        }

        if let Some(grade_level_id) = params.grade_level_id {
            query.push_str(&format!(" AND t.grade_level_id = {}", grade_level_id));
        }

        if let Some(teaching_subject) = &params.teaching_subject {
            query.push_str(" AND '");
            query.push_str(teaching_subject);
            query.push_str("' = ANY(t.teaching_subjects)");
        }

        query.push_str(" ORDER BY t.created_at DESC");
        query.push_str(&format!(" LIMIT {} OFFSET {}", page_size, offset));

        // 执行查询
        let teachers = sqlx::query_as::<_, TeacherListVO>(&query)
            .fetch_all(&mut *conn)
            .await?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM teachers WHERE 1=1";
        let total = sqlx::query_scalar::<_, i64>(count_query)
            .fetch_one(&mut *conn)
            .await?;

        Ok(PageResult {
            page,
            page_size,
            total,
            total_pages: (total + page_size - 1) / page_size,
            data: teachers,
        })
    }

    /// 根据ID获取教师详细信息
    pub async fn get_teacher_by_id(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
    ) -> Result<Option<TeacherDetailVO>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let teacher = sqlx::query_as::<_, TeacherDetailVO>(
            "SELECT 
                t.id,
                t.tenant_id,
                t.user_id,
                t.employee_id,
                t.teacher_name,
                t.phone,
                t.email,
                t.gender,
                t.date_of_birth,
                t.id_card_number,
                t.highest_education,
                t.graduation_school,
                t.major,
                t.hire_date,
                t.employment_status,
                t.title,
                t.teaching_subjects,
                t.homeroom_class_id,
                c.name as homeroom_class_name,
                t.grade_level_id,
                g.name as grade_level_name,
                t.subject_group_id,
                sg.name as subject_group_name,
                t.office_location,
                t.bio,
                t.is_active,
                t.created_at,
                t.updated_at
            FROM teachers t
            LEFT JOIN classes c ON t.homeroom_class_id = c.id
            LEFT JOIN public.grade_levels g ON t.grade_level_id = g.id
            LEFT JOIN public.subject_groups sg ON t.subject_group_id = sg.id
            WHERE t.id = $1",
        )
        .bind(teacher_id)
        .fetch_optional(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 删除教师
    pub async fn delete_teacher(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
    ) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let result = sqlx::query("DELETE FROM teachers WHERE id = $1")
            .bind(teacher_id)
            .execute(&mut *conn)
            .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("教师不存在".to_string()));
        }

        Ok(())
    }

    /// 激活/停用教师
    pub async fn toggle_teacher_status(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
        is_active: bool,
    ) -> Result<Teacher, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let teacher = sqlx::query_as::<_, Teacher>(
            r#"
            UPDATE teachers SET
                is_active = $2,
                updated_at = NOW()
            WHERE id = $1
            RETURNING *
            "#,
        )
        .bind(teacher_id)
        .bind(is_active)
        .fetch_one(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 检查工号是否已存在
    async fn employee_id_exists(
        &self,
        schema_name: &str,
        employee_id: &str,
    ) -> Result<bool, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM teachers WHERE employee_id = $1")
                .bind(employee_id)
                .fetch_one(&mut *conn)
                .await?;

        Ok(count > 0)
    }

    /// 根据手机号查找教师
    pub async fn get_teacher_by_phone(
        &self,
        schema_name: &str,
        phone: &str,
    ) -> Result<Option<Teacher>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let teacher = sqlx::query_as::<_, Teacher>(
            "SELECT * FROM teachers WHERE phone = $1 AND is_active = true"
        )
        .bind(phone)
        .fetch_optional(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 检查用户是否已经是教师
    async fn user_already_teacher(
        &self,
        schema_name: &str,
        user_id: Uuid,
    ) -> Result<bool, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM teachers WHERE user_id = $1")
                .bind(user_id)
                .fetch_one(&mut *conn)
                .await?;

        Ok(count > 0)
    }

    /// 检查工号是否与其他教师冲突
    async fn employee_id_conflicts(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
        employee_id: &str,
    ) -> Result<bool, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM teachers WHERE employee_id = $1 AND id != $2",
        )
        .bind(employee_id)
        .bind(teacher_id)
        .fetch_one(&mut *conn)
        .await?;

        Ok(count > 0)
    }

    /**
     * 作者：张瀚
     * 说明：通过教师ID批量查询
     */
    pub async fn find_all_by_id_in(
        &self,
        schema_name: &str,
        teacher_id_list: Vec<Uuid>,
    ) -> Result<Vec<Teacher>, String> {
        if teacher_id_list.is_empty() {
            return Ok(vec![]);
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select t.* from {}.teachers t where t.id in( ",
            schema_name
        ));
        // 使用 separated() 方法安全构建 IN 子句
        let mut separated = builder.separated(", ");
        for id in &teacher_id_list {
            separated.push_bind(id);
        }
        separated.push_unseparated(") order by t.created_at desc");
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    pub async fn get_teacher_summaries(
        &self,
        schema_name: &String,
        is_active: Option<bool>,
    ) -> anyhow::Result<Vec<TeacherSummary>> {
        let sql = match is_active {
            Some(_) => format!(
                "SELECT id, teacher_name as name, is_active FROM {}.teachers WHERE is_active = $1",
                schema_name
            ),
            None => format!("SELECT id, teacher_name as name, is_active FROM {}.teachers", schema_name),
        };

        let subjects = if let Some(active) = is_active {
            sqlx::query_as::<_, TeacherSummary>(&sql)
                .bind(active)
                .fetch_all(&self.db_pool)
                .await?
        } else {
            sqlx::query_as::<_, TeacherSummary>(&sql)
                .fetch_all(&self.db_pool)
                .await?
        };
        Ok(subjects)
    }

    /**
     * 作者：张瀚
     * 说明：按条件查询教师列表
     */
    pub async fn find_all(
        &self,
        schema_name: &String,
        params: &FindAllParams,
    ) -> Result<Vec<Teacher>, String> {
        let FindAllParams { name_like } = params;
        let mut builder =
            sqlx::QueryBuilder::new(format!("select * from {}.teachers t ", schema_name));
        if name_like.is_some() {
            builder.push("where ");
            if let Some(name_like_value) = name_like {
                builder.push(format!("t.teacher_name like '%{}%'", name_like_value));
            }
        }
        builder.push("order by created_at desc");
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：分页查询老师列表（管理员）
     */
    pub async fn page_all_teacher(
        &self,
        schema_name: &str,
        params: &PageAllTeacherParams,
    ) -> Result<(Vec<Teacher>, i64), String> {
        let PageAllTeacherParams {
            name_like,
            phone,
            page_params,
            employee_id,
            employment_status,
            is_active,
        } = params;

        // 构建基础查询
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT t.* FROM {}.teachers t WHERE 1=1 ",
            schema_name
        ));
        
        let mut count_builder = sqlx::QueryBuilder::new(format!(
            "SELECT COUNT(*) FROM {}.teachers t WHERE 1=1 ",
            schema_name
        ));

        // 添加查询条件
        if let Some(emp_id) = employee_id {
            builder.push(" AND t.employee_id = ").push_bind(emp_id);
            count_builder.push(" AND t.employee_id = ").push_bind(emp_id);
        }

        if let Some(name) = name_like {
            builder.push(" AND t.teacher_name LIKE '%' || ").push_bind(name).push(" || '%'");
            count_builder.push(" AND t.teacher_name LIKE '%' || ").push_bind(name).push(" || '%'");
        }

        if let Some(phone_num) = phone {
            builder.push(" AND t.phone = ").push_bind(phone_num);
            count_builder.push(" AND t.phone = ").push_bind(phone_num);
        }

        if let Some(status) = employment_status {
            builder.push(" AND t.employment_status = ").push_bind(status);
            count_builder.push(" AND t.employment_status = ").push_bind(status);
        }

        if let Some(active) = is_active {
            builder.push(" AND t.is_active = ").push_bind(active);
            count_builder.push(" AND t.is_active = ").push_bind(active);
        }

        // 添加排序和分页
        builder
            .push(" ORDER BY t.created_at DESC")
            .push(" LIMIT ")
            .push_bind(page_params.get_limit())
            .push(" OFFSET ")
            .push_bind(page_params.get_offset());

        // 执行查询
        let count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        let teachers = builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;

        Ok((teachers, count))
    }

    /**
     * 作者：张瀚
     * 说明：创建老师
     */
    pub async fn create_teacher(
        &self,
        schema_name: &str,
        params: &CreateTeacherParams,
    ) -> Result<Teacher, String> {
        let CreateTeacherParams {
            user_id,
            employee_id,
            teacher_name,
            phone,
            email,
            gender,
            date_of_birth,
            id_card_number,
            highest_education,
            graduation_school,
            major,
            hire_date,
            employment_status,
            title,
            office_location,
            is_active,
            ..
        } = params;
        info!("create teacher: {:?}, schema: {}", params, schema_name);
        let sql = format!("INSERT INTO {}.teachers (
                user_id, employee_id, teacher_name, phone, email, gender,
                date_of_birth, id_card_number, highest_education, graduation_school,
                major, hire_date, employment_status, title,
                office_location, is_active
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
            RETURNING *",schema_name);
        sqlx::query_as::<_, Teacher>(&sql)
        .bind(user_id)
        .bind(employee_id)
        .bind(teacher_name)
        .bind(phone)
        .bind(email)
        .bind(gender)
        .bind(date_of_birth)
        .bind(id_card_number)
        .bind(highest_education)
        .bind(graduation_school)
        .bind(major)
        .bind(hire_date)
        .bind(employment_status)
        .bind(title)
        .bind(office_location)
        .bind(is_active)
        .fetch_one(&self.db_pool)
        .await.map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：更新老师
     */
    pub async fn update_teacher(
        &self,
        schema_name: &str,
        _tenant_id: &Option<Uuid>,
        params: &UpdateTeacherParams,
    ) -> Result<Teacher, String> {
        let UpdateTeacherParams {
            id,
            employee_id,
            teacher_name,
            phone,
            email,
            gender,
            date_of_birth,
            id_card_number,
            highest_education,
            graduation_school,
            major,
            hire_date,
            employment_status,
            title,
            teaching_subjects,
            homeroom_class_id,
            grade_level_id,
            subject_group_id,
            office_location,
            bio,
            is_active,
            user_id,
        } = params;
        sqlx::query_as::<_, Teacher>(&format!(
            "UPDATE {}.teachers SET
                user_id = $1,
                employee_id = $2,
                teacher_name = $3,
                phone = $4,
                email = $5,
                gender = $6,
                date_of_birth = $7,
                id_card_number = $8,
                highest_education = $9,
                graduation_school = $10,
                major = $11,
                hire_date = $12,
                employment_status = $13,
                title = $14,
                teaching_subjects = $15,
                homeroom_class_id = $16,
                grade_level_id = $17,
                subject_group_id = $18,
                office_location = $19,
                bio = $20,
                is_active = $21,
                updated_at = NOW()
            WHERE id = $22
            RETURNING *",
            schema_name
        ))
        .bind(user_id)
        .bind(employee_id)
        .bind(teacher_name)
        .bind(phone)
        .bind(email)
        .bind(gender)
        .bind(date_of_birth)
        .bind(id_card_number)
        .bind(highest_education)
        .bind(graduation_school)
        .bind(major)
        .bind(hire_date)
        .bind(employment_status)
        .bind(title)
        .bind(teaching_subjects)
        .bind(homeroom_class_id)
        .bind(grade_level_id)
        .bind(subject_group_id)
        .bind(office_location)
        .bind(bio)
        .bind(is_active)
        .bind(id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())
    }




    /// 获取学科组映射
    pub async fn get_subject_group_map(&self, schema_name: &str) -> Result<HashMap<String, Uuid>, String> {
        let sql = format!(
            "SELECT sg.id, s.name as subject_name, gl.name as grade_name
             FROM {}.subject_groups sg
             LEFT JOIN public.subjects s ON s.code = sg.subject_code
             LEFT JOIN public.grade_levels gl ON gl.code = sg.grade_level_code
             WHERE sg.is_active = true",
            schema_name
        );

        let rows = sqlx::query(&sql)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| format!("查询学科组失败: {}", e))?;

        let mut map = HashMap::new();
        for row in rows {
            let id: Uuid = row.get("id");
            let subject_name: Option<String> = row.get("subject_name");
            let grade_name: Option<String> = row.get("grade_name");

            if let Some(subject) = subject_name {
                if let Some(grade) = grade_name {
                    // 创建复合键：学科名称 + 年级名称
                    let composite_key = format!("{}_{}", subject, grade);
                    map.insert(composite_key, id);
                } else {
                    // 兼容没有年级信息的旧数据
                    map.insert(subject, id);
                }
            }
        }

        Ok(map)
    }

    /// 获取行政班映射
    pub async fn get_admin_class_map(&self, schema_name: &str) -> Result<HashMap<String, Uuid>, String> {
        let sql = format!(
            "SELECT id, code FROM {}.administrative_classes WHERE is_active = true",
            schema_name
        );

        let rows = sqlx::query(&sql)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| format!("查询行政班失败: {}", e))?;

        let mut map = HashMap::new();
        for row in rows {
            let id: Uuid = row.get("id");
            let code: Option<String> = row.get("code");
            if let Some(class_code) = code {
                map.insert(class_code, id);
            }
        }

        Ok(map)
    }
}
