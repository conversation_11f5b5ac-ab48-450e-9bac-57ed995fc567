use crate::model::workflow::workflow::{
    InfoObject, WorkflowApiResponseItem, WorkflowQueryParams, WorkflowSettingCreateParams,
    WorkflowSettingDeleteParams, WorkflowSettingQueryParams, WorkflowSettingSummaryQueryParams,
    WorkflowSettingVO, WorkflowSummaryVO,
};
use crate::model::{PageParams, PageResult};
use anyhow::{bail, Result};
use chrono::Utc;
use reqwest::Client;
use sqlx::types::Json;
use sqlx::{PgPool, Postgres, QueryBuilder};
use std::collections::{HashMap, HashSet};
use std::env;
use uuid::Uuid;

pub struct WorkflowService {
    db: PgPool,
}

impl WorkflowService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// 获取工作流摘要列表，并同步更新数据库中的工作流信息。
    ///
    /// 该方法会：
    /// 1. 调用外部服务获取最新工作流数据；
    /// 2. 解析并转换为内部结构；
    /// 3. 与数据库中现有数据对比，执行新增、更新和停用操作；
    /// 4. 返回最新的工作流摘要信息。
    ///
    /// # Arguments
    /// * `params` - 查询参数，包含工作流类型等信息。
    ///
    /// # Returns
    /// 返回处理后的 `WorkflowSummaryVO` 列表。
    ///
    /// Author: tuip123
    pub async fn get_workflow_summaries(
        &self,
        params: WorkflowQueryParams,
    ) -> Result<Vec<WorkflowSummaryVO>, anyhow::Error> {
        // 1. 读取配置
        let base_url = env::var("WORKFLOW_SERVICE_ENDPOINT")?;
        let api_path = env::var("WORKFLOW_SERVICE_API")?; // /api/workflows/category
        let category = params.workflow_type.to_string();

        // 拼接 URL
        let full_url = format!("http://{}{}{}", base_url, api_path, category);

        // 2. 请求 AI 服务
        let client = Client::new();
        let raw_response = client
            .get(&full_url)
            .send()
            .await?
            .error_for_status()?
            .json::<Vec<WorkflowApiResponseItem>>()
            .await?;

        // 3. 构造响应数据（增加错误处理）
        let result: Vec<WorkflowSummaryVO> = raw_response
            .into_iter()
            .map(|item| {
                serde_json::from_str::<InfoObject>(&item.info)
                    .map_err(|e| {
                        anyhow::anyhow!("解析 info 字段失败，工作流: {}，错误: {}", item.name, e)
                    })
                    .map(|info_obj| WorkflowSummaryVO {
                        workflow_id: item.id,
                        workflow_name: item.name,
                        workflow_type: item.category,
                        description: info_obj.description,
                    })
            })
            .collect::<Result<Vec<_>, _>>()?; // 这里会提前返回错误

        // 4. 数据库操作（使用事务批量处理）
        let mut tx = self.db.begin().await?; // 开启事务

        // 获取数据库现有数据（包含更多字段）
        #[derive(sqlx::FromRow)]
        struct DbRecord {
            id: Uuid,
            workflow_name: String,
            description: String,
        }

        let db_existing: Vec<DbRecord> = sqlx::query_as(
            r#"SELECT id, workflow_name, description FROM public.workflows
                    WHERE workflow_type = $1"#,
        )
        .bind(category.clone()) // 直接使用引用，避免 clone
        .fetch_all(&mut *tx)
        .await?;

        // 创建快速查找结构
        let db_map: HashMap<Uuid, DbRecord> = db_existing.into_iter().map(|r| (r.id, r)).collect();

        // 准备批量操作容器
        let mut to_insert = Vec::new();
        let mut to_update = Vec::new();
        let mut active_ids = HashSet::new();

        // 遍历API结果
        for item in &result {
            active_ids.insert(item.workflow_id);

            match db_map.get(&item.workflow_id) {
                None => {
                    // 新记录需要插入
                    to_insert.push(item);
                }
                Some(db_record) => {
                    // 检查是否需要更新
                    if db_record.workflow_name != item.workflow_name
                        || db_record.description != item.description
                    {
                        to_update.push(item);
                    }
                }
            }
        }

        // 批量插入新记录
        if !to_insert.is_empty() {
            let mut builder = QueryBuilder::new(
                // 添加 is_active 字段
                "INSERT INTO public.workflows (id, workflow_name, workflow_type, description, is_active) "
            );
            builder.push_values(to_insert, |mut b, item| {
                b.push_bind(item.workflow_id)
                    .push_bind(&item.workflow_name)
                    .push_bind(category.clone()) // 直接使用引用
                    .push_bind(&item.description)
                    .push_bind(true); // 设置 is_active = TRUE
            });
            builder.build().execute(&mut *tx).await?;
        }

        // 批量更新变化记录 - 使用 QueryBuilder
        if !to_update.is_empty() {
            let mut builder = QueryBuilder::new(
                "UPDATE public.workflows AS w SET
                        workflow_name = u.workflow_name,
                        description = u.description,
                        is_active = TRUE,
                        updated_at = NOW()
                     FROM (",
            );

            builder.push_values(to_update, |mut b, item| {
                b.push_bind(item.workflow_id)
                    .push_bind(&item.workflow_name)
                    .push_bind(&item.description);
            });

            builder.push(") AS u(id, workflow_name, description) WHERE w.id = u.id");

            builder.build().execute(&mut *tx).await?;
        }

        // 停用不存在的记录（使用NOT IN优化）
        let deactivate_query = "
            UPDATE public.workflows
            SET is_active = FALSE,
                updated_at = NOW()
            WHERE workflow_type = $1
              AND id <> ALL($2)"; // 使用 <> ALL() 过滤

        let active_ids_vec: Vec<Uuid> = active_ids.into_iter().collect();
        sqlx::query(deactivate_query)
            .bind(category)
            .bind(&active_ids_vec)
            .execute(&mut *tx)
            .await?;

        tx.commit().await?; // 提交事务

        // 5. 返回给前端
        Ok(result)
    }

    /// 根据查询条件获取工作流设置列表，支持分页。
    ///
    /// 支持按学科、年级、题型、schema（租户）、工作流类型等字段进行过滤，
    /// 返回符合条件的工作流设置信息集合。
    ///
    /// # Arguments
    /// * `params` - 查询参数，包括工作流类型、学科代码、年级代码、题型代码和 schema 名称等。
    ///
    /// # Returns
    /// 返回包含工作流设置信息的分页结果。
    ///
    /// # Examples
    /// ```rust
    /// let params = WorkflowSettingQueryParams {
    ///     subject_code: Some("CHINESE".into()),
    ///     grade_level_code: Some("G1".into()),
    ///     workflow_type: Some(WorkflowCategory::Correction),
    ///     ..Default::default()
    /// };
    /// let pageResult = service.get_workflow_settings(params).await?;
    /// ```
    ///
    /// Author: tuip123
    pub async fn get_workflow_settings(
        &self,
        params: WorkflowSettingQueryParams,
    ) -> Result<PageResult<WorkflowSettingVO>> {
        fn build_join_clause<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            params: &'a WorkflowSettingQueryParams,
        ) {
            builder.push(" JOIN public.workflows w ON ws.workflow_id = w.id");
            if let Some(workflow_type) = &params.workflow_type {
                builder
                    .push(" AND w.workflow_type = ")
                    .push_bind(workflow_type);
            }
        }

        fn build_where_clause<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            params: &'a WorkflowSettingQueryParams,
        ) {
            let mut where_count = 0;

            if let Some(subject_code) = &params.subject_code {
                if !subject_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.subject_codes @> to_jsonb(");
                    builder.push_bind(vec![subject_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.subject_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(grade_level_code) = &params.grade_level_code {
                if !grade_level_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.grade_level_codes @> to_jsonb(");
                    builder.push_bind(vec![grade_level_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.grade_level_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(question_type_code) = &params.question_type_code {
                if !question_type_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.question_type_codes @> to_jsonb(");
                    builder.push_bind(vec![question_type_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.question_type_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(schema_name) = &params.schema_name {
                if !schema_name.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.schema_names @> to_jsonb(");
                    builder.push_bind(vec![schema_name.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.schema_names) = 0) ");
                    where_count += 1;
                }
            }
        }

        // 构建 COUNT 查询
        let mut count_builder =
            QueryBuilder::new("SELECT COUNT(*) as total FROM public.workflow_settings ws");
        build_join_clause(&mut count_builder, &params);
        build_where_clause(&mut count_builder, &params);

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT ws.id, ws.workflow_id, w.workflow_name, w.workflow_type, w.description, 
                        ws.subject_codes, ws.grade_level_codes, ws.question_type_codes, 
                        ws.schema_names, ws.created_at, ws.updated_at, ws.priority
                 FROM public.workflow_settings ws",
        );
        build_join_clause(&mut query_builder, &params);
        build_where_clause(&mut query_builder, &params);

        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();
        query_builder
            .push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);

        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db)
            .await?;

        let question_types: Vec<WorkflowSettingVO> =
            query_builder.build_query_as().fetch_all(&self.db).await?;

        Ok(PageResult {
            data: question_types,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    /// 根据查询条件获取工作流设置中关联的工作流摘要列表。
    ///
    /// 支持按学科、年级、题型、schema（租户）、工作流类型等字段进行过滤，
    /// 返回符合条件的工作流摘要信息集合。
    ///
    /// # Arguments
    /// * `params` - 查询参数，包括工作流类型、学科代码、年级代码、题型代码和 schema 名称等。
    ///
    /// # Returns
    /// 返回符合条件的工作流摘要信息列表。
    ///
    /// # Examples
    /// ```rust
    /// let params = WorkflowSettingSummaryQueryParams {
    ///     subject_code: Some("CHINESE".into()),
    ///     grade_level_code: Some("G1".into()),
    ///     workflow_type: Some(WorkflowCategory::Correction),
    ///     ..Default::default()
    /// };
    /// let summaries = service.get_workflow_summaries_in_setting(params).await?;
    /// ```
    ///
    /// Author: tuip123
    pub async fn get_workflow_summaries_in_setting(
        &self,
        params: WorkflowSettingSummaryQueryParams,
    ) -> Result<Vec<WorkflowSummaryVO>> {
        fn build_join_clause<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            params: &'a WorkflowSettingSummaryQueryParams,
        ) {
            builder.push(" JOIN public.workflows w ON ws.workflow_id = w.id");
            if let Some(workflow_type) = &params.workflow_type {
                builder
                    .push(" AND w.workflow_type = ")
                    .push_bind(workflow_type);
            }
        }

        fn build_where_clause<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            params: &'a WorkflowSettingSummaryQueryParams,
        ) {
            let mut where_count = 0;

            if let Some(subject_code) = &params.subject_code {
                if !subject_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.subject_codes @> to_jsonb(");
                    builder.push_bind(vec![subject_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.subject_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(grade_level_code) = &params.grade_level_code {
                if !grade_level_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.grade_level_codes @> to_jsonb(");
                    builder.push_bind(vec![grade_level_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.grade_level_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(question_type_code) = &params.question_type_code {
                if !question_type_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.question_type_codes @> to_jsonb(");
                    builder.push_bind(vec![question_type_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.question_type_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(schema_name) = &params.schema_name {
                if !schema_name.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.schema_names @> to_jsonb(");
                    builder.push_bind(vec![schema_name.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.schema_names) = 0) ");
                    where_count += 1;
                }
            }
        }

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT DISTINCT ON (ws.workflow_id) ws.workflow_id, w.workflow_name, w.workflow_type, w.description
                 FROM public.workflow_settings ws",
        );
        build_join_clause(&mut query_builder, &params);
        build_where_clause(&mut query_builder, &params);
        query_builder.push(" ORDER BY ws.workflow_id, ws.priority ASC");

        let question_types: Vec<WorkflowSummaryVO> =
            query_builder.build_query_as().fetch_all(&self.db).await?;

        Ok(question_types)
    }

    /// 创建工作流设置。
    ///
    /// # Arguments
    /// * `params` - 工作流设置参数。
    ///
    /// # Returns
    /// 返回创建结果。
    ///
    /// Author: tuip123
    pub async fn create_workflow_setting(&self, params: WorkflowSettingCreateParams) -> Result<()> {
        // 替换 Option<Vec<String>> 为 Vec<String>（若为 None 则使用空数组）
        let subject_codes = Json(params.subject_codes.unwrap_or_default());
        let grade_level_codes = Json(params.grade_level_codes.unwrap_or_default());
        let question_type_codes = Json(params.question_type_codes.unwrap_or_default());
        let schema_names = Json(params.schema_names.unwrap_or_default());
        let priority = params.priority.unwrap_or(100);

        sqlx::query(
            r#"
        INSERT INTO public.workflow_settings (
            workflow_id,
            subject_codes,
            grade_level_codes,
            question_type_codes,
            schema_names,
            priority
        )
        VALUES ($1, $2, $3, $4, $5, $6)
        "#,
        )
        .bind(params.workflow_id)
        .bind(subject_codes)
        .bind(grade_level_codes)
        .bind(question_type_codes)
        .bind(schema_names)
        .bind(priority)
        .execute(&self.db)
        .await?;

        Ok(())
    }

    /// 更新工作流设置。
    ///
    /// # Arguments
    /// * `params` - 工作流设置参数。
    ///
    /// # Returns
    /// 返回结果。
    ///
    /// Author: tuip123
    pub async fn update_workflow_setting(&self, params: WorkflowSettingCreateParams) -> Result<()> {
        let workflow_setting_id = match params.workflow_setting_id {
            Some(id) => id,
            None => bail!("缺少 workflow_setting_id"),
        };

        let subject_codes = Json(params.subject_codes.unwrap_or_default());
        let grade_level_codes = Json(params.grade_level_codes.unwrap_or_default());
        let question_type_codes = Json(params.question_type_codes.unwrap_or_default());
        let schema_names = Json(params.schema_names.unwrap_or_default());
        let priority = params.priority.unwrap_or(100);

        sqlx::query(
            r#"
        UPDATE workflow_settings
        SET
            workflow_id = $1,
            subject_codes = $2,
            grade_level_codes = $3,
            question_type_codes = $4,
            schema_names = $5,
            updated_at = $6,
            priority = $7
        WHERE id = $8
        "#,
        )
        .bind(params.workflow_id)
        .bind(subject_codes)
        .bind(grade_level_codes)
        .bind(question_type_codes)
        .bind(schema_names)
        .bind(Utc::now())
        .bind(priority)
        .bind(workflow_setting_id)
        .execute(&self.db)
        .await?;

        Ok(())
    }

    /// 删除工作流设置。
    ///
    /// # Arguments
    /// * `params` - 工作流设置参数。
    ///
    /// # Returns
    /// 删除结果。
    ///
    /// Author: tuip123
    pub async fn delete_workflow_setting(&self, params: WorkflowSettingDeleteParams) -> Result<()> {
        sqlx::query(
            r#"
                    DELETE FROM public.workflow_settings WHERE id = $1
                "#,
        )
        .bind(params.workflow_setting_id)
        .execute(&self.db)
        .await?;

        Ok(())
    }
}
