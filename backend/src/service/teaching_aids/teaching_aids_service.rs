use crate::model::paper::paper::{CreatePaperRequest, Paper, PaperQuery};
use crate::model::public_resource::book::Book;
use crate::model::public_resource::catalog::Catalog;
use crate::model::public_resource::question::{Question, QuestionUnit};
use crate::model::public_resource::question_answer::QuestionAnswer;
use crate::model::public_resource::section::SectionQuestionRef;
use crate::model::public_resource::section::SectionUnit;
use crate::model::public_resource::section::{QuestionGroupData, Section};
use crate::model::public_resource::vo::section_vo::{QuestionAnswerAreaDetail, SectionDetail, SectionQuestionDetail};
use crate::model::teaching_aids::textbook_paper::CreateTextbookPaperRequest;
use crate::model::textbooks::*;
use crate::model::{PageParams, PageResult};
use crate::service::paper::paper::PaperService;
use crate::service::storage::StorageService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use crate::utils::error::AppError;
use crate::utils::error_handler::AppResult;
use anyhow::{bail, Result};
use axum::extract::Query;
use chrono::Utc;
use serde_json::Value;
use sqlx::types::Json;
use sqlx::{PgPool, Postgres, QueryBuilder};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{error, log};
use uuid::Uuid;

#[derive(Clone)]
pub struct TeachingAidsService {
    pool: PgPool,
    storage_service: Arc<dyn StorageService>,
    paper_service: Arc<PaperService>,
    textbook_paper_service: Arc<TextbookPaperService>,
}

impl TeachingAidsService {
    pub fn new(pool: PgPool, storage_service: Arc<dyn StorageService>, paper_service: Arc<PaperService>, textbook_paper_service: Arc<TextbookPaperService>) -> Self {
        Self {
            pool,
            storage_service,
            paper_service,
            textbook_paper_service,
        }
    }

    pub async fn create_textbook(&self, request: Textbook) -> AppResult<Textbook> {
        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
            "#,
            request.id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            request.created_at,
            request.updated_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(textbook)
    }

    pub async fn get_textbook(&self, id: Uuid) -> AppResult<TextbookVO> {
        let textbook = sqlx::query_as!(
            TextbookVO,
            r#"
                    SELECT t.*, s.name AS subject, g.name AS grade_level
                    FROM public.textbooks AS t
                    LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                    LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                    WHERE t.id = $1"#,
            id
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn get_book_test(&self, id: Uuid) -> Result<BookVO> {
        let book = sqlx::query_as::<_, BookVO>(
            "
                SELECT b.*, s.name AS subject,g.name AS grade_level FROM public.books b
                    LEFT JOIN public.subjects AS s ON b.subject_code = s.code
                    LEFT JOIN public.grade_levels AS g ON b.grade_level_code = g.code
                    WHERE b.id = $1
                ",
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;
        book.ok_or(anyhow::anyhow!("Book not found"))
    }

    pub async fn get_textbooks(&self) -> AppResult<Vec<TextbookVO>> {
        // 直接映射到TextbookVO，通过SQL别名确保字段与结构体字段匹配
        let textbooks = sqlx::query_as!(
            TextbookVO,
            r#"
                SELECT
                    t.id AS "id!: Uuid",
                    t.title AS "title!: String",
                    t.subject_id,
                    s.name AS subject,
                    t.grade_level_id,
                    g.name AS grade_level,
                    t.publisher,
                    t.publication_year,
                    t.cover_path,
                    t.isbn,
                    t.version,
                    t.status,
                    t.creator_id,
                    t.created_at,
                    t.updated_at
                FROM public.textbooks AS t
                LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                "#
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(textbooks)
    }

    pub async fn get_textbooks_test(&self, params: BookQuery) -> Result<PageResult<Book>> {
        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>, // 改为可变引用
            params: &BookQuery,
        ) {
            let mut where_count = 0;
            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    let pattern = format!("%{}%", search.trim());

                    builder.push(" (b.id::text ILIKE ").push_bind(pattern.clone()).push(" OR b.title ILIKE ").push_bind(pattern).push(")");
                    let _ = where_count + 1;
                }
            }
            if let Some(subject_code) = &params.subject_code {
                if !subject_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" b.subject_code = ").push_bind(subject_code.to_string());
                    where_count += 1;
                }
            }
            if let Some(gl_code) = &params.grade_level_code {
                if !gl_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" b.grade_level_code = ").push_bind(gl_code.to_string());
                    where_count += 1;
                }
            }
        }

        let mut count_builder = QueryBuilder::new("SELECT COUNT(*) FROM public.books b");
        build_where_clause(&mut count_builder, &params);

        let mut query_builder = QueryBuilder::new("SELECT * FROM public.books b");
        build_where_clause(&mut query_builder, &params);

        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        query_builder.push(" LIMIT ").push_bind(page_size).push(" OFFSET ").push_bind(offset);

        let total: i64 = count_builder.build_query_scalar().fetch_one(&self.pool).await?;
        let data = query_builder.build_query_as::<Book>().fetch_all(&self.pool).await?;

        Ok(PageResult {
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
            data,
        })
    }

    pub async fn find_latest_textbooks(&self, limit: i64) -> AppResult<Vec<TextbookVO>> {
        let textbooks = sqlx::query_as!(
            TextbookVO,
            r#"
                SELECT
                    t.id AS "id!: Uuid",
                    t.title AS "title!: String",
                    t.subject_id,
                    s.name AS subject,
                    t.grade_level_id,
                    g.name AS grade_level,
                    t.publisher,
                    t.publication_year,
                    t.cover_path,
                    t.isbn,
                    t.version,
                    t.status,
                    t.creator_id,
                    t.created_at,
                    t.updated_at
                FROM public.textbooks AS t
                LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                order by t.created_at desc
                limit $1
                "#,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(textbooks)
    }

    pub async fn update_textbooks_test(&self, book_id: Uuid, request: BookUpdatePayload) -> Result<Book> {
        // 把中文逗号分割成 Vec<String>
        let authors_vec: Vec<String> = request.authors.split('，').map(|s| s.trim().to_string()).filter(|s| !s.is_empty()).collect();

        let updated: Book = sqlx::query_as::<_, Book>(
            r#"
            UPDATE books
            SET
                title = $1,
                authors = $2,
                subject_code = $3,
                grade_level_code = $4,
                updated_at = NOW()
            WHERE id = $5
            RETURNING *
            "#,
        )
        .bind(&request.title)
        .bind(&authors_vec)
        .bind(&request.subject_code)
        .bind(&request.grade_level_code)
        .bind(book_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(updated)
    }

    pub async fn get_textbook_catalog_handler_test(&self, book_id: Uuid) -> Result<Vec<CatalogTreeNode>> {
        let catalogues = sqlx::query_as!(
            Catalog,
            r#"
            SELECT
                c.id AS "id!: Uuid",
                c.book_id,
                c.parent_id,
                c.serial,
                c.level,
                c.title,
                c.section_id,
                c.updated_at
            FROM public.catalogs AS c
            WHERE c.book_id = $1
            ORDER BY c.serial ASC
            "#,
            book_id
        )
        .fetch_all(&self.pool)
        .await?;
        let tree = self.build_catalog_tree(catalogues)?;

        Ok(tree)
    }

    pub fn build_catalog_tree(&self, catalogs: Vec<Catalog>) -> Result<Vec<CatalogTreeNode>> {
        // 1. 按level升序排序，serial升序排序
        let mut catalogs = catalogs;
        catalogs.sort_by(|a, b| a.level.cmp(&b.level).then_with(|| a.serial.cmp(&b.serial)));

        // 2. 按层级分组
        let mut level_groups: HashMap<i16, HashMap<Uuid, CatalogTreeNode>> = HashMap::new();
        let mut min_level = i16::MAX;
        let mut max_level = i16::MIN;

        for catalog in catalogs {
            let level = catalog.level;
            let node = CatalogTreeNode {
                id: catalog.id,
                book_id: catalog.book_id,
                parent_id: catalog.parent_id,
                serial: catalog.serial,
                level: catalog.level,
                title: catalog.title.clone(),
                section_id: catalog.section_id,
                updated_at: catalog.updated_at,
                children: Vec::new(),
            };

            level_groups.entry(level).or_insert_with(HashMap::new).insert(catalog.id, node);

            min_level = min_level.min(level);
            max_level = max_level.max(level);
        }

        // 是否可能不连续？目前假设level连续
        let mut tree: Vec<CatalogTreeNode> = Vec::new();
        for level in min_level..=max_level {
            // 解构返回的 [Option<&mut _>; 2]
            if let [Some(now_map), Some(parent_map)] = level_groups.get_disjoint_mut([&level, &(level + 1)]) {
                // 先收集要移除的 keys，避免在遍历时同时 mutably borrow 同一个 map
                let ids: Vec<Uuid> = now_map.keys().cloned().collect();

                for id in ids {
                    if let Some(node) = now_map.remove(&id) {
                        match node.parent_id {
                            None => {
                                // 没父节点 → 是根，放到 tree
                                tree.push(node);
                            }
                            Some(pid) => {
                                // 找父节点的可变引用并 push
                                if let Some(parent_node) = parent_map.get_mut(&pid) {
                                    parent_node.children.push(node);
                                } else {
                                    // todo 判断是否需要处理
                                    bail!("父节点 {} 找不到，WHY", pid);
                                }
                            }
                        }
                    }
                }
            }
        }

        for (_, map) in level_groups.into_iter() {
            for (_, node) in map {
                tree.push(node); // 把剩下的节点全部加到 tree
            }
        }

        // 后处理排序
        fn sort_children_by_serial(node: &mut CatalogTreeNode) {
            // 先对子节点排序
            node.children.sort_by(|a, b| a.serial.cmp(&b.serial));

            // 然后递归对子节点的子节点排序
            for child in &mut node.children {
                sort_children_by_serial(child);
            }
        }

        tree.sort_by(|a, b| a.serial.cmp(&b.serial));
        for node in tree.iter_mut() {
            sort_children_by_serial(node);
        }

        Ok(tree)
    }

    pub async fn get_section_handler_test(&self, book_id: Uuid, catalog_id: Uuid) -> Result<Option<SectionVO>> {
        // 1. 查询整本书的目录
        let catalogs = sqlx::query_as!(
            Catalog,
            r#"
            SELECT
                c.id AS "id!: Uuid",
                c.book_id,
                c.parent_id,
                c.serial,
                c.level,
                c.title,
                c.section_id,
                c.updated_at
            FROM public.catalogs AS c
            WHERE c.book_id = $1
            ORDER BY c.serial ASC
            "#,
            book_id
        )
        .fetch_all(&self.pool)
        .await?;

        // 2. 构建树
        let tree = self.build_catalog_tree(catalogs)?;

        // 3. 找到对应节点
        fn find_node(nodes: &[CatalogTreeNode], target_id: Uuid) -> Option<&CatalogTreeNode> {
            for node in nodes {
                if node.id == target_id {
                    return Some(node);
                }
                if let Some(found) = find_node(&node.children, target_id) {
                    return Some(found);
                }
            }
            None
        }

        // 4. 找到第一个带 section_id 的节点
        let node = match find_node(&tree, catalog_id).and_then(|node| node.find_first_section()) {
            Some(node) => node,
            None => return Ok(None), // 没有找到
        };

        let section = sqlx::query_as!(
            Section,
            r#"SELECT s.id, s.answer_card_id, s.items as "items: Json<Vec<SectionUnit<SectionQuestionRef>>>", s.snapshots, s.updated_at
                FROM public.sections AS s WHERE s.id = $1"#,
            node.section_id
        )
        .fetch_one(&self.pool)
        .await?;
        let section_detail = self.convert_section_to_detail(section).await?;
        // todo 这里获取到一个section的id，需要获取到其确切内容，补充试题信息。返回完整的catalog回前端之后前端需要对应设置选中。
        let vo: SectionVO = SectionVO {
            catalog: node.clone(),
            section: section_detail,
        };
        Ok(Some(vo))
    }

    async fn convert_section_to_detail(&self, section: Section) -> Result<SectionDetail> {
        let mut detail_items = Vec::new();

        for unit in section.items.0 {
            // unwrap Json<Vec<_>>
            match unit {
                SectionUnit::Text(text) => {
                    detail_items.push(SectionUnit::Text(text));
                }
                SectionUnit::QuestionGroup(group) => {
                    let mut detailed_questions = Vec::new();

                    for qref in group.questions {
                        // 1. 查询 Question
                        let question: Option<Question> = sqlx::query_as!(
                            Question,
                            r#"SELECT q.id, q.question_type_code, q.items as "items: Json<Vec<QuestionUnit>>", q.subject_code, q.updated_at FROM public.questions q WHERE id = $1"#,
                            qref.question_id
                        )
                        .fetch_optional(&self.pool)
                        .await?;

                        // 2. 转换 answer_refs → answer_area_details
                        let mut answer_area_details = Vec::new();
                        for aref in qref.answer_refs {
                            let answers: Vec<QuestionAnswer> = sqlx::query_as!(QuestionAnswer, "SELECT * FROM public.question_answers WHERE id = ANY($1)", &aref.answer_ids)
                                .fetch_all(&self.pool)
                                .await?;

                            answer_area_details.push(QuestionAnswerAreaDetail {
                                score: aref.score,
                                answer_area_id: aref.answer_area_id,
                                answer_area_number: aref.answer_area_number,
                                answers,
                            });
                        }

                        detailed_questions.push(SectionQuestionDetail {
                            question_id: qref.question_id,
                            question,
                            question_number: qref.question_number,
                            answer_area_details,
                        });
                    }

                    detail_items.push(SectionUnit::QuestionGroup(QuestionGroupData {
                        text: group.text,
                        questions: detailed_questions,
                    }));
                }
            }
        }

        Ok(SectionDetail {
            id: section.id,
            answer_card_id: section.answer_card_id,
            snapshots: section.snapshots,
            items: detail_items,
        })
    }

    pub async fn update_textbook(&self, id: Uuid, request: Textbook) -> AppResult<Textbook> {
        let now = Utc::now();

        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            UPDATE public.textbooks
            SET title = $2, subject_id = $3, grade_level_id = $4, publisher = $5, publication_year = $6, isbn = $7, version = $8, status = $9, creator_id = $10, updated_at = $11
            WHERE id = $1
            RETURNING *
            "#,
            id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            now
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn delete_textbook(&self, id: Uuid) -> AppResult<()> {
        let result = sqlx::query!("DELETE FROM public.textbooks WHERE id = $1", id).execute(&self.pool).await?;

        if result.rows_affected() == 0 {
            return Err(anyhow::anyhow!("Textbook not found"));
        }

        Ok(())
    }

    pub async fn create_textbook_with_chapters(&self, textbook: Textbook, chapters: Vec<TeachingAidChapter2>, answer_sheets: Vec<(Uuid, String, Value)>) -> AppResult<Textbook> {
        let mut tx = self.pool.begin().await?;

        let created_textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, cover_path, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            RETURNING *
            "#,
            textbook.id,
            textbook.title,
            textbook.subject_id,
            textbook.grade_level_id,
            textbook.publisher,
            textbook.publication_year,
            textbook.cover_path,
            textbook.isbn,
            textbook.version,
            textbook.status,
            textbook.creator_id,
            textbook.created_at,
            textbook.updated_at
        )
        .fetch_one(&mut *tx)
        .await?;

        for chapter in chapters {
            log::info!("Chapter ID: {}", chapter.id.clone());
            sqlx::query!(
                r#"
                INSERT INTO public.chapters (id, textbook_id, parent_id, chapter_number, title, content, metadata, creator_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                "#,
                chapter.id,
                textbook.id,
                chapter.parent_id,
                chapter.chapter_number,
                chapter.title,
                chapter.content,
                chapter.metadata,
                chapter.creator_id,
                chapter.created_at,
                chapter.updated_at,
            )
            .execute(&mut *tx)
            .await?;
        }

        let mut couter = 0;
        for (id, title, content) in answer_sheets {
            let create_paper_req = CreatePaperRequest {
                id: Some(id),
                paper_name: title,
                paper_content: serde_json::from_value(content)?,
            };
            let new_paper = self.paper_service.create_paper_tx(&mut tx, create_paper_req).await?;

            let create_textbook_paper_req = CreateTextbookPaperRequest {
                paper_id: new_paper.id,
                textbook_id: created_textbook.id,
                serial_number: couter,
            };
            self.textbook_paper_service.create_textbook_paper_tx(&mut tx, create_textbook_paper_req).await?;
            couter += 1;
        }

        tx.commit().await?;

        Ok(created_textbook)
    }

    pub async fn create_textbook_with_chapters_test(
        &self,
        books: Vec<BookJson>,
        catalogs: Vec<CatalogJson>,
        questions: Vec<QuestionJson>,
        answers: Vec<AnswerJson>,
        sections: Vec<SectionJson>,
    ) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        // 插入书籍
        for book in books {
            sqlx::query!(
                r#"
            INSERT INTO books (
                id, title, subject_code, grade_level_code, publisher, distributor,
                year, cover_path, isbn, edition, printing_version, authors, summary
            )
            VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13)
            ON CONFLICT (id) DO UPDATE SET
                title = EXCLUDED.title,
                subject_code = EXCLUDED.subject_code,
                grade_level_code = EXCLUDED.grade_level_code,
                publisher = EXCLUDED.publisher,
                distributor = EXCLUDED.distributor,
                year = EXCLUDED.year,
                cover_path = EXCLUDED.cover_path,
                isbn = EXCLUDED.isbn,
                edition = EXCLUDED.edition,
                printing_version = EXCLUDED.printing_version,
                authors = EXCLUDED.authors,
                summary = EXCLUDED.summary
            "#,
                book.id,
                book.title,
                book.subject_code,
                book.grade_level_code,
                book.publisher,
                book.distributor,
                book.year,
                book.cover_path,
                book.isbn,
                book.edition,
                book.printing_version,
                //     todo
                &book.authors.unwrap_or(vec![]),
                book.summary
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入目录
        for catalog in catalogs {
            sqlx::query!(
                r#"
            INSERT INTO catalogs (id, book_id, parent_id, section_id, serial, level, title)
            VALUES ($1,$2,$3,$4,$5,$6,$7)
            ON CONFLICT (id) DO UPDATE SET
                parent_id = EXCLUDED.parent_id,
                section_id = EXCLUDED.section_id,
                serial = EXCLUDED.serial,
                level = EXCLUDED.level,
                title = EXCLUDED.title
            "#,
                catalog.id,
                catalog.book_id,
                catalog.parent_id,
                catalog.section_id,
                catalog.serial,
                catalog.level,
                catalog.title,
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入题目
        for question in questions {
            sqlx::query!(
                r#"
            INSERT INTO questions (id, question_type_code, items, subject_code)
            VALUES ($1,$2,$3,$4)
            ON CONFLICT (id) DO UPDATE SET
                question_type_code = EXCLUDED.question_type_code,
                items = EXCLUDED.items,
                subject_code = EXCLUDED.subject_code
            "#,
                question.id,
                question.question_type_code,
                Value::Array(question.items), // serde_json::Value
                //     todo
                question.subject_code.unwrap_or("".to_string()),
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入答案
        for answer in answers {
            sqlx::query!(
                r#"
            INSERT INTO question_answers (id, question_id, answer_area_id, content, explanation)
            VALUES ($1,$2,$3,$4,$5)
            ON CONFLICT (id) DO UPDATE SET
                question_id = EXCLUDED.question_id,
                answer_area_id = EXCLUDED.answer_area_id,
                content = EXCLUDED.content,
                explanation = EXCLUDED.explanation
            "#,
                answer.id,
                answer.question_id,
                answer.answer_area_id,
                answer.content,
                answer.explanation,
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入 Section
        for section in sections {
            sqlx::query!(
                r#"
            INSERT INTO sections (id, answer_card_id, items, snapshots)
            VALUES ($1,$2,$3,$4)
            ON CONFLICT (id) DO UPDATE SET
                answer_card_id = EXCLUDED.answer_card_id,
                items = EXCLUDED.items,
                snapshots = EXCLUDED.snapshots
            "#,
                section.id,
                section.answer_card_id,
                Value::Array(section.items), // serde_json::Value
                &section.snapshots
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    pub async fn get_chapters_by_textbook_id(&self, textbook_id: Uuid) -> AppResult<Vec<TeachingAidChapter2>> {
        let chapters = sqlx::query_as!(
            TeachingAidChapter2,
            "SELECT id, textbook_id, parent_id, chapter_number, title, description, content, metadata, creator_id, created_at, updated_at FROM public.chapters WHERE textbook_id = $1",
            textbook_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(chapters)
    }

    pub async fn get_chapter_content(&self, chapter_id: Uuid) -> Result<Option<serde_json::Value>, AppError> {
        let result: Option<Option<serde_json::Value>> = sqlx::query_scalar!(r#"SELECT content FROM public.chapters WHERE id = $1"#, chapter_id)
            .fetch_optional(&self.pool)
            .await
            .map_err(|err| {
                error!("查询章节内容失败, chapter_id: {}, error: {}", chapter_id, err);
                AppError::InternalServerError("查询章节内容失败".to_string())
            })?;

        Ok(result.flatten())
    }
}
