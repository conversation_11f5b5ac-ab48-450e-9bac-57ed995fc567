use sqlx::PgPool;
use uuid::Uuid;
use crate::model::{
    subject_groups::subject_groups::{
        CreateSubjectGroupsParams, SubjectGroups, SubjectGroupsDetail, UpdateSubjectGroupsParams,
    },
};
use crate::utils::error::AppError;

#[derive(Clone)]
pub struct SubjectGroupsService {
    db_pool: PgPool,
}

///学科组管理服务
impl SubjectGroupsService {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /**
     * 作者：张瀚
     * 说明：新建学科组
     */
    pub async fn create_subject_groups(
        &self,
        schema_name: &String,
        params: &CreateSubjectGroupsParams,
    ) -> Result<SubjectGroups, String> {
        let CreateSubjectGroupsParams {
            group_name,
            subject_code,
            grade_level_code,
            description,
            leader_user_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.subject_groups (group_name,subject_code,grade_level_code,description,leader_user_id)VALUES ( ",
            schema_name
        ));
        builder
            .push_bind(group_name)
            .push(" , ")
            .push_bind(subject_code)
            .push(" , ")
            .push_bind(grade_level_code)
            .push(" , ")
            .push_bind(description)
            .push(" , ")
            .push_bind(leader_user_id)
            .push(" ) returning *");
        //创建
        Ok(builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询所有的学科组
     */
    pub async fn find_all(&self, schema_name: &String) -> Result<Vec<SubjectGroupsDetail>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select sg.*,s.\"name\" as \"subject_name\",t.teacher_name,gl.\"name\" as \"grade_level_name\" from {}.subject_groups sg left join public.subjects s on s.code = sg.subject_code left join {}.teachers t on t.id = sg.leader_user_id left join public.grade_levels gl on gl.code = sg.grade_level_code order by sg.is_active desc, created_at desc",
            schema_name, schema_name
        ));
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：通过ID批量查询学科组
     */
    pub async fn find_all_by_id_list(
        &self,
        schema_name: &String,
        subject_groups_ids: &Vec<Uuid>,
    ) -> Result<Vec<SubjectGroups>, String> {
        if subject_groups_ids.len() == 0 {
            return Ok(vec![]);
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select * from {}.subject_groups sg where sg.id in ( ",
            schema_name
        ));
        for (index, item) in subject_groups_ids.iter().enumerate() {
            builder.push_bind(item);
            if index < subject_groups_ids.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) order by created_at desc");
            }
        }
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 说明：根据ID获取单个学科组信息
     */
    pub async fn get_subject_group_by_id(
        &self,
        schema_name: &String,
        subject_group_id: &Uuid,
    ) -> Result<Option<SubjectGroups>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * FROM {}.subject_groups WHERE id = ",
            schema_name
        ));
        builder.push_bind(subject_group_id);

        let result = builder
            .build_query_as()
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;

        Ok(result)
    }

    pub async fn update_subject_groups(
        &self,
        schema_name: &String,
        params: &UpdateSubjectGroupsParams,
    ) -> Result<SubjectGroups, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "UPDATE {}.subject_groups SET ",
            schema_name
        ));

        let mut needs_comma = false;

        // 动态构建更新字段
        if let Some(group_name) = &params.group_name {
            builder.push("group_name = ").push_bind(group_name);
            needs_comma = true;
        }

        if let Some(subject_code) = &params.subject_code {
            if needs_comma { builder.push(", "); }
            builder.push("subject_code = ").push_bind(subject_code);
            needs_comma = true;
        }

        if let Some(grade_level_code) = &params.grade_level_code {
            if needs_comma { builder.push(", "); }
            builder.push("grade_level_code = ").push_bind(grade_level_code);
            needs_comma = true;
        }

        if params.description.is_some() {
            if needs_comma { builder.push(", "); }
            builder.push("description = ").push_bind(&params.description);
            needs_comma = true;
        }

        if needs_comma { builder.push(", "); }
        match &params.leader_user_id {
            Some(leader_id) => {
                builder.push("leader_user_id = ").push_bind(leader_id);
            },
            None => {
                builder.push("leader_user_id = NULL");
            }
        }
        needs_comma = true;

        // 如果没有提供任何更新字段，直接返回错误
        if !needs_comma {
            return Err("No fields to update".to_string());
        }

        // 添加更新时间和WHERE条件
        builder.push(", updated_at = NOW() WHERE id = ").push_bind(&params.id);

        // 执行更新并返回更新后的记录
        let result = builder
            .push(" RETURNING *")
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;

        Ok(result)
    }

    pub async fn delete_subject_group(
        &self,
        schema_name: &String,
        id: Uuid,
    ) -> anyhow::Result<(), AppError> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "UPDATE {}.subject_groups SET is_active = false, updated_at = NOW() WHERE id = ",
            schema_name
        ));
        builder.push_bind(id);

        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(AppError::from)?;

        Ok(())
    }


}
