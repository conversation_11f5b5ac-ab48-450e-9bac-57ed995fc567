use crate::controller::administrative_classes::administrative_classes_controller::StudentHomeworkAnalysis;
use crate::model::classes::classes::ClassesSummary;
use crate::model::homework_students::homework_students::{HomeworkStudentStatus, HomeworkStudents};
use crate::model::paper::paper_cache::ScoringCriterion;
use crate::model::score::{Score, ScoreStatus};
use rust_xlsxwriter::{Workbook};
use std::io::Cursor;
use crate::repository::homework_students::homework_students_repository::{HomeworkStudentBase, HomeworkStudentsRepository};
use crate::repository::score::score_repository;
use crate::repository::score::score_repository::fetch_all_scores;
use crate::repository::students::class_repository::fetch_teaching_classes_summary;
use bigdecimal::{BigDecimal, ToPrimitive};
use serde::Serialize;
use sqlx::PgPool;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tracing::debug;
use uuid::Uuid;
use crate::repository::students::student_repository::StudentBase;
use crate::service::grading::vo::score_vo::{scores_to_score_vos};
use crate::service::homework::vo::{AnalysisSummary, ContentCriteria};
use crate::service::homework::vo::analysis_vo::{AnswerAnalysis, ClassAnswerAnalysisVo, ContentAnswerAnalysis};
use crate::service::homework::vo::homework_student::homework_students_to_student_vos;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::service::storage::StorageService;

pub struct HomeworkAnalysisService {
    db: PgPool,
    tenant_name: String,
    storage: Arc<dyn StorageService>,
}

impl HomeworkAnalysisService {
    pub fn new(db: PgPool, tenant_name: String, storage: Arc<dyn StorageService>) -> HomeworkAnalysisService {
        HomeworkAnalysisService { db, tenant_name, storage }
    }

    pub async fn classes_summary(&self, homework_id: Uuid, criteria_list: Vec<ScoringCriterion>) -> anyhow::Result<ClassSummaryResponse> {
        let students = HomeworkStudentsRepository::fetch_homework_students_by_homework_class(&self.db, self.tenant_name.as_str(), homework_id, None).await?;
        let mut student_class_map = HashMap::new();
        students.into_iter().for_each(|student| {
            student_class_map.entry(student.class_id).or_insert_with(Vec::new).push(student);
        });
        let mut total_score = 0f64;
        let criteria_ids: Vec<_> = criteria_list
            .iter()
            .map(|c| {
                total_score += c.score;
                c.id.clone()
            })
            .collect();

        let mut summary = AnalysisSummary::default();
        summary.total_score = total_score;
        let mut criteria_score_map: HashMap<Uuid, (i64, f64)> = HashMap::new();
        let mut class_vos = Vec::new();
        for (k, v) in student_class_map {
            let mut class_vo = ClassAnalysisVo::default();
            class_vo.class = fetch_teaching_classes_summary(&self.db, self.tenant_name.as_str(), k).await?;
            class_vo.total_score = total_score;
            let mut student_ids = Vec::new();
            v.iter().for_each(|student| match student.status {
                HomeworkStudentStatus::Unsubmitted => {
                    class_vo.absent_student_count += 1;
                }
                HomeworkStudentStatus::Error => {
                    class_vo.error_student_count += 1;
                }
                HomeworkStudentStatus::Done => {
                    student_ids.push(student.student_id);
                    class_vo.scoring_student_count += 1;
                }
            });
            summary.absent_student_count += class_vo.absent_student_count;
            summary.error_student_count += class_vo.error_student_count;
            summary.scoring_student_count += class_vo.scoring_student_count;
            let criteria_summaries = score_repository::fetch_criteria_summary(&self.db, self.tenant_name.as_str(), Some(criteria_ids.clone()), Some(student_ids)).await?;
            let mut criteria_summary_map: HashMap<Uuid, (i64, i64, f64)> = HashMap::new();
            criteria_summaries.into_iter().for_each(|summary| {
                let ss = match summary.status {
                    ScoreStatus::Done|ScoreStatus::CheckedCorrect|ScoreStatus::CheckedError => {
                        let score = summary.total_score.to_f64().unwrap_or(0.0);
                        (summary.count, summary.count, score)
                    },
                    _ => (summary.count, 0, 0f64)
                };
                criteria_summary_map
                    .entry(summary.criteria_id)
                    .and_modify(|value| {
                        value.0 += ss.0;
                        value.1 += ss.1;
                        value.2 += ss.2;
                    })
                    .or_insert(ss);
            });
            for (sk, sv) in criteria_summary_map {
                class_vo.total_score_count += sv.0;
                class_vo.done_score_count += sv.1;
                if sv.1 > 0 {
                    class_vo.avg_score += sv.2 / sv.1 as f64;
                }
                // 计算各个小题对应的总分和数量，用于求平均分
                criteria_score_map
                    .entry(sk)
                    .and_modify(|value| {
                        value.0 += sv.1;
                        value.1 += sv.2;
                    })
                    .or_insert((sv.1, sv.2));
            }
            summary.total_score_count += class_vo.total_score_count;
            summary.done_score_count += class_vo.done_score_count;
            class_vos.push(class_vo);
        }
        for (_k, v) in criteria_score_map {
            if v.0 > 0 {
                summary.avg_score += v.1 / v.0 as f64;
            }
        }
        Ok(ClassSummaryResponse { summary, details: class_vos })
    }
    pub async fn classes_detail(&self, homework_id: Uuid, class_id: Uuid, criteria_list: Vec<ScoringCriterion>) -> anyhow::Result<ClassDetailResponse> {
        let mut total_score = 0f64;
        let criteria_ids: Vec<_> = criteria_list
            .iter()
            .map(|c| {
                total_score += c.score;
                c.id.clone()
            })
            .collect();
        let mut summary = AnalysisSummary::default();
        summary.total_score = total_score;
        let mut sum_stu_score = 0f64;

        let students = HomeworkStudentsRepository::fetch_students_by_homework_class(&self.db, self.tenant_name.as_str(), homework_id, Some(class_id)).await?;
        let mut student_scores = Vec::new();
        for student in students {
            let scores = match student.status {
                HomeworkStudentStatus::Done => {
                    summary.scoring_student_count += 1;
                    let scores = fetch_all_scores(&self.db, self.tenant_name.as_str(), Some(criteria_ids.clone()), Some(vec![student.student_id])).await?;
                    let stu_score = scores.iter().fold(0f64, |acc, s| match s.score_status {
                        ScoreStatus::Done|ScoreStatus::CheckedCorrect|ScoreStatus::CheckedError => acc + s.score.to_f64().unwrap_or(0f64),
                        _ => acc,
                    });
                    sum_stu_score += stu_score;
                    Some(scores)
                }
                HomeworkStudentStatus::Error => {
                    summary.error_student_count += 1;
                    None
                }
                HomeworkStudentStatus::Unsubmitted => {
                    summary.absent_student_count += 1;
                    None
                }
            };
            student_scores.push(HomeworkStudentScoresVo { student, scores });
        }
        summary.avg_score = if summary.scoring_student_count > 0 {
            sum_stu_score / summary.scoring_student_count as f64
        } else {
            0f64
        };
        Ok(ClassDetailResponse {
            summary,
            criteria_list,
            student_scores,
        })
    }
    /// 获取学生作业排名
    pub async fn get_student_homework_rank(&self, homework_students: &Vec<HomeworkStudents> ,student_id_list: &Vec<Uuid>) -> anyhow::Result<HashMap<Uuid, Vec<StudentHomeworkAnalysis>>> {
        let mut result=HashMap::new();
        //1、遍历每一个作业
        let homework_id_list:HashSet<Uuid>=homework_students.iter().map(|hs| hs.homework_id).collect();
        for homework_id in homework_id_list{
            ///todo待优化，可以在循环前进行预分组来减少循环次数，提高性能（可用 HashMap<homework_id, HashMap<class_id, Vec<&HomeworkStudents>>> 结构）
            //2、获取该作业下的所有学生
            let students:Vec<&HomeworkStudents>=homework_students.iter().filter(|hs| hs.homework_id==homework_id).collect();
            //3、按教学班分组
            let mut class_group_map: HashMap<Uuid, Vec<(&Uuid, &Option<BigDecimal>)>> = HashMap::new();
            for student in students.iter() {
                class_group_map.entry(student.class_id).or_insert_with(Vec::new).push((&student.student_id,&student.scores));
            }
            //4 、遍历每一个教学班、分配排名
            let mut student_rank_map: HashMap<Uuid, i32> = HashMap::new();
            for (_class_id, mut students_scores) in class_group_map {
                //按分数降序排列
                students_scores.sort_by(|a, b| {
                    let score_a = a.1.as_ref().and_then(|s| s.to_f64());
                    let score_b = b.1.as_ref().and_then(|s| s.to_f64());
                    score_b.partial_cmp(&score_a).unwrap_or(std::cmp::Ordering::Equal)
                });
                let mut rank = 1;
                let mut last_score = None;
                let mut last_rank = 1;
                for (i, (student_id, score)) in students_scores.iter().enumerate() {
                    let score_f64 = score.as_ref().and_then(|s| s.to_f64()).unwrap_or(0.0);
                    //只有当分数发生变化，才更新排名
                    if Some(score_f64) != last_score {
                        rank = i + 1;
                        last_score = Some(score_f64);
                        last_rank = rank as i32;
                    }
                    if student_id_list.contains(student_id) {
                        student_rank_map.insert(**student_id, last_rank);
                    }
                }
            }
            //构建结果
            for student_id in student_id_list {
                if let Some(hs) = homework_students.iter().find(|h| h.student_id == *student_id && h.homework_id == homework_id) {
                    let score = hs.scores.as_ref().and_then(|s| s.to_f64()).unwrap_or(0.0);
                    // 判断是否缺考,缺考排名设置为0
                    let rank = if hs.status == HomeworkStudentStatus::Unsubmitted {
                        0
                    } else {
                        student_rank_map.get(student_id).copied().unwrap_or(0)
                    };
                    let analysis=StudentHomeworkAnalysis{
                        homework_id,
                        status: hs.status.clone(),
                        score,
                        rank,
                        teaching_class_id: hs.class_id,
                    };
                    result.entry(*student_id).or_insert_with(Vec::new).push(analysis);
                }
            }
        }
        Ok(result)
    }

    pub async fn class_analysis(&self, homework_id: Uuid, class_id: Uuid) -> anyhow::Result<ClassAnswerAnalysisVo> {
        // class_id homework_id 查出所有学生
        // 列出缺考学生，异常学生： 异常学生可点击查看题卡
        let students = HomeworkStudentsRepository::fetch_students_by_homework_class(&self.db, self.tenant_name.as_str(), homework_id, Some(class_id)).await?;
        let (done_students, error_students, unsubmitted_students) = homework_students_to_student_vos(students.clone())?;
        let student_ids = students.iter().map(|s| s.student_id).collect::<Vec<_>>();
        let student_info_map: HashMap<_, _> = students.into_iter().map(|s| (s.student_id, StudentBase {
            student_id: s.student_id,
            student_number: s.student_number,
            student_name: s.student_name,
        })).collect();
        // 获取试卷结构
        let (content_criteria, criteria_ids) = HomeworkPapersService::new(self.db.clone(), self.tenant_name.clone()).get_content_and_criteria_by_homework_id(homework_id).await?;
        // criteria_ids class_id 查出所有scores
        let scores = fetch_all_scores(&self.db, self.tenant_name.as_str(), Some(criteria_ids.clone()), Some(student_ids)).await?;
        debug!("class_analysis score len:{}", scores.len());
        let scores = scores_to_score_vos(&self.db, self.tenant_name.as_str(), self.storage.clone(), scores, student_info_map).await?;
        let mut criteria_scores_map: HashMap<Uuid, Vec<_>> = HashMap::new();
        scores.into_iter().for_each(|score| {
            criteria_scores_map.entry(score.criteria_id).or_insert(Vec::new()).push(score);
        });
        // 按打分点展开，第一题：异常（同学A，同学B）待批阅（同学C） 分数分布：0分（同学X）平均分
        let content_answer_analysis_list: Vec<_> = content_criteria.into_iter().map(|content_criteria| {
            match content_criteria {
                ContentCriteria::Text(text) => {ContentAnswerAnalysis::Text(text)}
                ContentCriteria::Criteria(c) => {
                    if let Some(scores) = criteria_scores_map.get(&c.id).cloned() {
                        ContentAnswerAnalysis::AnswerAnalysis(AnswerAnalysis::from_scores(c, scores))
                    } else {
                        ContentAnswerAnalysis::AnswerAnalysis(AnswerAnalysis::blank_criterion(c))
                    }
                }
            }
        }).collect();
        Ok(ClassAnswerAnalysisVo {
            done_students,
            error_students,
            unsubmitted_students,
            content_answer_analysis_list,
        })
    }

    pub async fn export_all_class_reports(&self, class_summaries: ClassSummaryResponse) -> anyhow::Result<Vec<u8>> {
        let mut workbook = Workbook::new();
        let worksheet_name = "班级报告".to_string();
        let worksheet = workbook.add_worksheet();
        worksheet.set_name(&worksheet_name)?;
        // 设置表头
        let headers = [
            "序号", "班级名称", "班级编号", "状态", "已评分人数", "缺考人数", "异常学生数", "平均分"
        ];
        for (col, header) in headers.iter().enumerate() {
            worksheet.write_string(0, col as u16, *header)?;
        }
        // 填充数据
        for (idx, class_vo) in class_summaries.details.iter().enumerate() {
            let row = (idx + 1) as u32;
            let status = if class_vo.class.is_active {
                "启用"
            } else {
                "禁用"
            };
            worksheet.write_number(row, 0, (idx + 1) as f64)?;
            worksheet.write_string(row, 1, &class_vo.class.name)?;
            worksheet.write_string(row, 2, &class_vo.class.code)?;
            worksheet.write_string(row, 3, status)?;
            worksheet.write_number(row, 4, class_vo.scoring_student_count as f64)?;
            worksheet.write_number(row, 5, class_vo.absent_student_count as f64)?;
            worksheet.write_number(row, 6, class_vo.error_student_count as f64)?;
            worksheet.write_number(row, 7, class_vo.avg_score)?;
        }

        // 写入到内存
        let mut buf = Cursor::new(Vec::new());
        workbook.save_to_writer(&mut buf)?;
        Ok(buf.into_inner())
    }
    pub async fn export_class_detail_report(&self,
        class_detail:ClassDetailResponse,
        criteria_list:Vec<ScoringCriterion>
    )->anyhow::Result<Vec<u8>>{
        let mut workbook = Workbook::new();
        let worksheet_name = "班级详细报告".to_string();
        let worksheet = workbook.add_worksheet();
        worksheet.set_name(&worksheet_name)?;
        // 设置表头
        let mut headers = vec!["序号", "学生姓名", "学号", "状态", "总分"];
        criteria_list.iter().for_each(|c| {
            if let Some(name)=&c.name{
                headers.push(name.as_str());
            }
        });
        for (col, header) in headers.iter().enumerate(){
            worksheet.write_string(0, col as u16, *header)?;
        }
        // 填充数据
        for (idx, student_score) in class_detail.student_scores.iter().enumerate(){
            let row= (idx +1) as u32;
            let status = match student_score.student.status{
                HomeworkStudentStatus::Done => "已评分",
                HomeworkStudentStatus::Error => "异常",
                HomeworkStudentStatus::Unsubmitted => "缺考",
            };
            worksheet.write_number(row, 0, (idx + 1) as f64)?;
            worksheet.write_string(row, 1, &student_score.student.student_name)?;
            worksheet.write_string(row, 2, &student_score.student.student_number)?;
            worksheet.write_string(row, 3, status)?;
            if let Some(scores)=&student_score.scores{
                let mut total_score=0f64;
                let mut score_map:HashMap<Uuid,f64>=HashMap::new();
                //构建题目和分数的映射
                scores.iter().for_each(|s| {
                    match s.score_status{
                        ScoreStatus::Done|ScoreStatus::CheckedCorrect|ScoreStatus::CheckedError=>{
                            let score=s.score.to_f64().unwrap_or(0.0);
                            total_score+=score;
                            score_map.insert(s.criteria_id,score);
                        },
                        _=>{}
                    }
                });
                worksheet.write_number(row,4,total_score)?;
                // 根据映射，写入各个小题的分数
                for (i,c) in criteria_list.iter().enumerate(){
                    if let Some(score)=score_map.get(&c.id){
                        worksheet.write_number(row,(i+5) as u16,*score)?;
                    }else{
                        worksheet.write_string(row,(i+5) as u16,"-")?;
                    }
                }
                }else{
                    worksheet.write_string(row,4,"-")?;
                    // 缺考或异常，直接写入"-"
                    for i in 0..criteria_list.len(){
                        worksheet.write_string(row,(i+5) as u16,"-")?;
                    }
                }
            }
        // 写入到内存
        let mut buf = Cursor::new(Vec::new());
        workbook.save_to_writer(&mut buf)?;
        Ok(buf.into_inner())
    }
}

#[derive(Serialize)]
pub struct ClassSummaryResponse {
    pub summary: AnalysisSummary,
    pub details: Vec<ClassAnalysisVo>,
}

#[derive(Serialize)]
pub struct ClassDetailResponse {
    pub summary: AnalysisSummary,
    criteria_list: Vec<ScoringCriterion>,
    student_scores: Vec<HomeworkStudentScoresVo>,
}

#[derive(Serialize)]
pub struct HomeworkStudentScoresVo {
    pub student: HomeworkStudentBase,
    pub scores: Option<Vec<Score>>,
}
#[derive(Serialize, Default)]
pub struct ClassAnalysisVo {
    class: ClassesSummary,
    scoring_student_count: i64,
    absent_student_count: i64,
    error_student_count: i64,
    total_score_count: i64,
    done_score_count: i64,
    avg_score: f64,
    total_score: f64,
}

