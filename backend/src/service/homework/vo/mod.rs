pub mod analysis_vo;
pub mod homework_student;

use crate::model::homework_students::homework_students;
use crate::model::homework_students::homework_students::HomeworkStudents;
use crate::model::paper::paper_cache::ScoringCriterion;
use crate::model::StudentBaseInfo;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debu<PERSON>, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudentsWithStudentBaseInfo {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub class_id: Uuid,
    pub student_base_info: Option<StudentBaseInfo>,
    pub status: homework_students::HomeworkStudentStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
impl HomeworkStudentsWithStudentBaseInfo {
    pub fn from(sbi: Option<StudentBaseInfo>, hs: HomeworkStudents) -> Self {
        Self {
            id: hs.id,
            homework_id: hs.homework_id,
            student_id: hs.student_id,
            class_id: hs.class_id,
            student_base_info: sbi,
            status: hs.status,
            created_at: hs.created_at,
            updated_at: hs.updated_at,
        }
    }
}

#[derive(Serialize, Default)]
pub struct AnalysisSummary {
    pub scoring_student_count: i64,
    pub absent_student_count: i64,
    pub error_student_count: i64,
    pub total_score_count: i64,
    pub done_score_count: i64,
    pub avg_score: f64,
    pub total_score: f64,
}
#[derive(Serialize, Default)]
pub struct HomeworkSummary {
    pub paper_id: Option<Uuid>,
    pub criteria: CriteriaSummary,
    pub scan: ScanSummary,
    pub homework_info: HomeworkInfoSummary,
    pub student: HomeworkStudentSummary,
    pub scoring: ScoringSummary,
}

#[derive(Serialize, Default)]
pub struct HomeworkInfoSummary {
    pub leaf_count: i32,
    pub leaf_total: i32,
    pub page_count: i32,
    pub page_total: i32,
}
#[derive(Serialize, Default)]
pub struct CriteriaSummary {
    pub count: i64,
    pub score: f64,
}
#[derive(Serialize, Default)]
pub struct ScanSummary {
    pub undistributed_count: i64,
    pub unbounded_count: i64,
    pub duplication_count: i64,
    pub error_count: i64,
    pub total_count: i64,
}
#[derive(Serialize, Default)]
pub struct HomeworkStudentSummary {
    pub absent_count: i64,
    pub error_count: i64,
    pub total_count: i64,
}
#[derive(Serialize, Default)]
pub struct ScoringSummary {
    pub total_count: i64,
    pub scored_count: i64,
    pub avg_score: f64,
}
#[derive(Serialize, Debug)]
pub enum ContentCriteria {
    Text(String),
    Criteria(ScoringCriterion),
}
