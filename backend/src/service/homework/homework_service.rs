use crate::model::grading::paper_scans::PaperScanStatus;
use crate::model::homework::homework::UpdateHomeworkLeafInfo;
use crate::model::homework_students::homework_students::HomeworkStudentStatus;
use crate::model::score::ScoreStatus;
use crate::model::PageParams;
use crate::repository::paper_scan_page::paper_scan_page_repository::PaperScanPageRepository;
use crate::repository::paper_scans::paper_scans_repository::PaperScansRepository;
use crate::repository::score::score_repository;
use crate::repository::subject_groups::subject_group_repository::SubjectGroupRepository;
use crate::service::homework::vo::HomeworkSummary;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::service::permission::casbin_service::CasbinPermissionService;
use crate::service::permission::data_filter::{DataFilterManager, FilterContext};
use crate::{
    middleware::auth_middleware::AuthContext,
    model::{
        homework::homework::{CreateHomeworkParams, Homework, HomeworkStatistics, UpdateHomeworkParams, HOMEWORK_STATUS_DONE, HOMEWORK_STATUS_DRAFT},
        StudentBaseInfo,
    },
    repository::{homework::homework_repository::HomeworkRepository, homework_students::homework_students_repository::HomeworkStudentsRepository},
    utils::schema::connect_with_schema,
};
use bigdecimal::ToPrimitive;
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, PgPool, Result};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{error, info, warn};
use uuid::Uuid;
use crate::utils::db::builder_in_list;

#[derive(Clone)]
pub struct HomeworkService {
    db_pool: PgPool,
    data_filter_manager: Option<Arc<DataFilterManager>>,
    casbin_service: Option<Arc<dyn CasbinPermissionService>>,
}

impl HomeworkService {
    pub fn new(db_pool: PgPool) -> Self {
        // 创建默认的数据过滤器管理器
        let data_filter_manager = Arc::new(DataFilterManager::create_default(db_pool.clone()));

        // 创建 MenuService 用于初始化 CasbinService
        let menu_service = Arc::new(crate::service::menu::MenuService::new(db_pool.clone()));

        // 创建 CasbinService（使用默认模型路径）
        let casbin_service = tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async { crate::service::permission::MultiTenantCasbinService::new(db_pool.clone(), "config/rbac_model.conf".to_string(), menu_service).await })
        });

        match casbin_service {
            Ok(service) => {
                // 使用 new_with_permission_filter 创建完整的服务实例
                Self::new_with_permission_filter(db_pool, data_filter_manager, Arc::new(service))
            }
            Err(_) => {
                // 如果 CasbinService 初始化失败，回退到基础版本
                Self {
                    db_pool,
                    data_filter_manager: Some(data_filter_manager),
                    casbin_service: None,
                }
            }
        }
    }

    pub fn new_with_permission_filter(db_pool: PgPool, data_filter_manager: Arc<DataFilterManager>, casbin_service: Arc<dyn CasbinPermissionService>) -> Self {
        Self {
            db_pool,
            data_filter_manager: Some(data_filter_manager),
            casbin_service: Some(casbin_service),
        }
    }
}

impl HomeworkService {
    /**
     * 作者：张瀚
     * 说明：获取作业管理统计数据
     */
    pub async fn get_statistics(&self, schema_name: &String, _context: AuthContext) -> Result<HomeworkStatistics, String> {
        //查询能看的作业列表
        let list = sqlx::query_as::<_, Homework>(format!("SELECT * FROM {}.homework h order by h.created_at desc", schema_name).as_str())
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;

        //统计
        let total_homeworks = list.len() as i32;
        let mut completed_homeworks = 0;
        let mut in_progress_homeworks = 0;
        let mut total_students = 0;
        let mut homework_id_list: Vec<Uuid> = vec![];
        for ele in list {
            if ele.homework_status == HOMEWORK_STATUS_DONE {
                completed_homeworks += 1;
            } else if ele.homework_status != HOMEWORK_STATUS_DRAFT {
                //非完成和草稿的都是进行中
                in_progress_homeworks += 1;
            }
            homework_id_list.push(ele.id);
        }
        if homework_id_list.len() > 0 {
            let mut builder = sqlx::QueryBuilder::new(format!("select distinct hs.student_id from {}.homework_students hs where hs.homework_id ", schema_name));
            builder_in_list(&mut builder, homework_id_list).map_err(|e| e.to_string())?;
            let student_id_list: Vec<Uuid> = builder.build_query_scalar().fetch_all(&self.db_pool).await.map_err(|e| e.to_string())?;
            total_students = student_id_list.len() as i32;
        }
        Ok(HomeworkStatistics {
            total_homeworks,
            completed_homeworks,
            in_progress_homeworks,
            total_students,
        })
    }

    /// 获取学科组题卡消耗统计
    pub async fn card_consume_statistics(&self, context: &AuthContext, schema_name: &str) -> anyhow::Result<Vec<CardConsumeStatistics>> {
        let mut statistics = Vec::new();

        // 如果没有配置权限过滤器，返回空列表（表示无权限）
        let (filter_manager, casbin_service) = match (&self.data_filter_manager, &self.casbin_service) {
            (Some(fm), Some(cs)) => (fm, cs),
            _ => {
                warn!("Permission filtering not configured for homework service");
                return Ok(statistics);
            }
        };

        let user_roles: Vec<String> = context
            .roles
            .iter()
            .filter(|role| role.schema_name == *schema_name && role.is_verified)
            .map(|role| role.identity_type.clone())
            .collect();

        if user_roles.is_empty() {
            info!("No roles found for user: {} in tenant: {}", context.username, schema_name);
            return Ok(statistics);
        }

        // 构建过滤上下文
        let filter_context = FilterContext {
            user_id: context.user_id,
            user_identity: user_roles.join(","),
            tenant_id: context.get_default_tenant_id(),
            schema_name: schema_name.to_string(),
            resource: "homework".to_string(),
            action: "read".to_string(),
        };

        // 根据学科组分组查询作业印发张数 (学科组ID,学科组名称,总张数,总页数 )
        let subject_group_homework_consume_statistics: Vec<(Uuid, String, String, i64, i64, i64)> =
            SubjectGroupRepository::subject_group_homework_consume_statistics(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
                .await
                .map_err(|e| {
                    error!("根据学科组分组查询作业印发张数");
                    e
                })?;

        // 查询扫描总张数
        let scanned_total_cards: Vec<(Uuid, String, String, i64)> =
            PaperScansRepository::subject_group_scan_total_cards(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
                .await
                .map_err(|e| {
                    error!("查询扫描总张数");
                    e
                })?;
        let scanned_total_cards_map = scanned_total_cards
            .into_iter()
            .fold(HashMap::new(), |mut map, (subject_group_id, _subject_group_code, _subject_group_name, leaf_total)| {
                map.insert(subject_group_id, leaf_total);
                map
            });

        // 查询扫描总张数
        let scanned_page_total = PaperScanPageRepository::count_by_subject_group_id(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
            .await
            .map_err(|e| {
                error!("查询扫描总页数异常: {}", e);
                e
            })?;
        let scanned_page_total_map = scanned_page_total
            .into_iter()
            .fold(HashMap::new(), |mut map, (subject_group_id, _subject_group_code, _subject_group_name, page_total)| {
                map.insert(subject_group_id, page_total);
                map
            });

        subject_group_homework_consume_statistics
            .into_iter()
            .for_each(|(subject_group_id, subject_code, subject_group_name, leaf_total, page_total, homework_total)| {
                statistics.push(CardConsumeStatistics {
                    subject_group_id,
                    subject_code: subject_code.clone(),
                    subject_name: subject_group_name.clone(),
                    leaf_total,
                    page_total,
                    scanned_leaf_total: scanned_total_cards_map.get(&subject_group_id).copied().unwrap_or(0) as i64,
                    scanned_page_total: scanned_page_total_map.get(&subject_group_id).copied().unwrap_or(0),
                    homework_total,
                })
            });

        Ok(statistics)
    }

    ///
    /// 该函数用于查询指定条件下的题卡消耗汇总指标数据，支持按学科组、行政班、教学班进行筛选
    ///
    /// # 参数说明
    ///
    /// # 参数说明
    /// * `schema_name` - 数据库模式名称，用于指定查询的指定租户数据
    /// * `homework_ids` - 作业ID列表，可选参数，用于筛选特定作业的数据
    /// * `subject_groups_ids` - 学科组ID列表，可选参数，用于筛选特定学科组的数据
    /// * `teaching_classes_ids` - 教学班ID列表，可选参数，用于筛选特定教学班的数据
    ///
    /// # 返回值
    /// 返回 `AssembleCardMetricsResponse` 类型的结果，包含题卡消耗的汇总指标信息
    ///
    /// # 错误处理
    /// 如果查询过程中发生错误，将返回包含错误信息的 `anyhow::Result`
    pub async fn get_assemble_card_metrics(&self, context: &AuthContext, schema_name: &str) -> anyhow::Result<AssembleCardMetricsResponse> {
        // 组装数据
        let mut assemble = AssembleCardMetricsResponse::default();

        // 如果没有配置权限过滤器，返回空列表（表示无权限）
        let (filter_manager, casbin_service) = match (&self.data_filter_manager, &self.casbin_service) {
            (Some(fm), Some(cs)) => (fm, cs),
            _ => {
                warn!("Permission filtering not configured for homework service");
                return Ok(assemble);
            }
        };

        if let Some(filter_context) = context.get_filter_context_with_tenant_name(schema_name.to_string(), "homework".to_string(), "read".to_string()) {
            // 查询作业印发张
            let (leaf_total, page_total) = HomeworkRepository::fetch_homework_leaf_total(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
                .await
                .map_err(|e| {
                    error!("查询作业印发信息异常: {}", e);
                    e
                })?;
            // 查询作业总数
            let homework_total = HomeworkRepository::count_by_homework_id(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
                .await
                .map_err(|e| {
                    error!("查询作业总数异常: {}", e);
                    e
                })?;
            // 查询扫描页面数
            let scanned_leaf_total = PaperScansRepository::count_by_homework_id(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
                .await
                .map_err(|e| {
                    error!("查询扫描页面数异常: {}", e);
                    e
                })?;
            // 查询扫描总张数
            let scanned_page_total = PaperScanPageRepository::count_by_homework_id(&self.db_pool, &filter_context, filter_manager.as_ref(), casbin_service.as_ref())
                .await
                .map_err(|e| {
                    error!("查询扫描总张数异常: {}", e);
                    e
                })?;
            assemble.leaf_total = leaf_total;
            assemble.page_total = page_total;
            assemble.homework_total = homework_total;
            assemble.scanned_leaf_total = scanned_leaf_total;
            assemble.scanned_page_total = scanned_page_total;

            Ok(assemble)
        } else {
            info!("No roles found for user: {} in tenant: {}", context.username, schema_name);
            Ok(assemble)
        }
    }

    pub async fn create_homework(&self, _context: &AuthContext, schema_name: &String, params: &CreateHomeworkParams) -> Result<Homework, String> {
        let CreateHomeworkParams {
            homework_name,
            homework_status,
            subject_group_id,
            description,
        } = params;
        let bean = sqlx::query_as::<_, Homework>(&format!(
            "INSERT INTO {}.homework (homework_name, homework_status, subject_group_id, description ) VALUES ($1, $2, $3, $4) RETURNING *",
            schema_name
        ))
        .bind(homework_name)
        .bind(homework_status)
        .bind(subject_group_id)
        .bind(description)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())?;
        //TODO:创建关联关系
        Ok(bean)
    }
    pub async fn update_homework_leaf_info(&self, _context: &AuthContext, schema_name: &String, params: &UpdateHomeworkLeafInfo) -> Result<Homework, String> {
        sqlx::query_as::<_, Homework>(&format!(
            "UPDATE {}.homework SET leaf_count = $1, leaf_total = $2, page_count = $3, page_total = $4, updated_at = NOW() WHERE ID = $5 RETURNING *",
            schema_name
        ))
        .bind(params.leaf_count)
        .bind(params.leaf_total)
        .bind(params.page_count)
        .bind(params.page_total)
        .bind(params.id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())
    }
    pub async fn update_homework(&self, _context: &AuthContext, schema_name: &String, params: &UpdateHomeworkParams) -> Result<Homework, String> {
        let UpdateHomeworkParams {
            id,
            homework_name,
            homework_status,
            subject_group_id,
            description,
        } = params;
        sqlx::query_as::<_, Homework>(&format!(
            "UPDATE {}.homework SET homework_name = $1, homework_status = $2, subject_group_id = $3, description = $4, updated_at = NOW() WHERE ID = $5 RETURNING *",
            schema_name
        ))
        .bind(homework_name)
        .bind(homework_status)
        .bind(subject_group_id)
        .bind(description)
        .bind(id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())
    }

    /**
     * 说明：分页查询所有作业
     * 直接在数据库层面应用权限过滤，性能更优
     */
    pub async fn page_all_homework_with_data_filter(
        &self,
        context: &AuthContext,
        schema_name: &String,
        page_params: &PageParams,
        name: &Option<String>,
        status: &Option<String>,
        subject_group_id: &Option<Uuid>,
    ) -> Result<(Vec<Homework>, i64), String> {
        // 检查权限过滤器是否可用
        let (filter_manager, casbin_service) = match (&self.data_filter_manager, &self.casbin_service) {
            (Some(fm), Some(cs)) => (fm, cs),
            _ => {
                warn!("Permission filtering not configured, return empty data");
                return Ok((vec![], 0));
            }
        };
        if let Some(filter_context) = context.get_filter_context_with_tenant_name(schema_name.to_string(), "homework".to_string(), "read".to_string()) {
            info!("Applying data filter for user {} in schema {}", context.user_id, schema_name);
            // 直接使用带过滤器的repository方法
            HomeworkRepository::page_all_homework_with_filter(
                &self.db_pool,
                page_params,
                name,
                status,
                subject_group_id,
                &schema_name,
                &filter_context,
                filter_manager.as_ref(),
                casbin_service.as_ref(),
            )
        .await
        .map_err(|e| e.to_string())
        } else {
            info!("No roles found for user: {} in tenant: {}", context.username, schema_name);
            Ok((vec![], 0))
        }
    }

    pub async fn page_student_homeworks(
        &self,
        context: &AuthContext,
        schema_name: &String,
        params: &PageParams,
        name: &Option<String>,
        status: &Option<String>,
        subject_group_id: &Option<Uuid>,
    ) -> Result<(Vec<Homework>, i64), String> {
        // 检查权限过滤器是否可用
        let (filter_manager, casbin_service) = match (&self.data_filter_manager, &self.casbin_service) {
            (Some(fm), Some(cs)) => (fm, cs),
            _ => {
                warn!("Permission filtering not configured, return empty data");
                return Ok((vec![], 0));
            }
        };
        if let Some(filter_context) = context.get_filter_context_with_tenant_name(schema_name.to_string(), "homework_student".to_string(), "read".to_string()) {
            info!("Applying data filter for user {} in schema {}", context.user_id, schema_name);
            // 直接使用带过滤器的repository方法
            HomeworkRepository::page_student_homeworks(&self.db_pool, schema_name.as_str(), &params,
                                                       name, status, subject_group_id,
                                                       &filter_context,
                                                       filter_manager.as_ref(),
                                                       casbin_service.as_ref(), )
                .await.map_err(|e| e.to_string())
        } else {
            info!("No roles found for user: {} in tenant: {}", context.username, schema_name);
            Ok((vec![], 0))
        }
    }
    /**
     * 作者：朱若彪
     * 说明：根据id查询作业
     */
    pub async fn get_homework_by_id(&self, _context: &AuthContext, schema_name: &String, id: Uuid) -> anyhow::Result<Homework> {
        let homework = HomeworkRepository::fetch_homework_by_id(&self.db_pool, schema_name, id).await?;
        Ok(homework)
    }
    pub async fn homework_summary(&self, schema_name: &str, id: Uuid) -> anyhow::Result<HomeworkSummary> {
        let mut summary = HomeworkSummary::default();
        let student_summaries = HomeworkStudentsRepository::fetch_homework_student_summary(&self.db_pool, schema_name, id).await?;
        student_summaries.into_iter().for_each(|s| {
            summary.student.total_count += s.count;
            match s.status {
                HomeworkStudentStatus::Unsubmitted => {
                    summary.student.absent_count += s.count;
                }
                HomeworkStudentStatus::Error => {
                    summary.student.error_count += s.count;
                }
                HomeworkStudentStatus::Done => {}
            }
        });
        let paper_service = HomeworkPapersService::new(self.db_pool.clone(), schema_name.to_string());
        let (criteria_ids, total_score) = paper_service.get_homework_criteria_ids(id).await?;
        let paper_id = paper_service
            .get_homework_papers_by_homework_id(id)
            .await
            .map_err(|e| anyhow::anyhow!(e))?
            .into_iter()
            .map(|p| p.id)
            .collect::<Vec<_>>()
            .pop();
        if paper_id.is_some() {
            summary.paper_id = paper_id;
            summary.criteria.score = total_score;
            summary.criteria.count = criteria_ids.len() as i64;
            let criteria_summaries = score_repository::fetch_criteria_summary(&self.db_pool, schema_name, Some(criteria_ids), None).await?;
            let paper_scans_status_summary_record = PaperScansRepository::calculate_scan_statistics(&self.db_pool, schema_name, id).await?;

            paper_scans_status_summary_record.into_iter().for_each(|s| {
                summary.scan.total_count += s.total;
                match s.status {
                    PaperScanStatus::Undistributed => summary.scan.undistributed_count += s.total,
                    PaperScanStatus::Unbound => summary.scan.unbounded_count += s.total,
                    PaperScanStatus::Duplicate => summary.scan.duplication_count += s.total,
                    PaperScanStatus::Error => summary.scan.error_count += s.total,
                    PaperScanStatus::Done => {}
                }
            });

            // 计算平均分
            criteria_summaries.into_iter().for_each(|criteria| {
                summary.scoring.total_count += criteria.count;
                match criteria.status {
                    ScoreStatus::Undistributed | ScoreStatus::Distributed | ScoreStatus::Excepted => {}
                    ScoreStatus::Done | ScoreStatus::CheckedCorrect | ScoreStatus::CheckedError => {
                        summary.scoring.scored_count += criteria.count;
                        if criteria.count > 0 {
                            summary.scoring.avg_score += criteria.total_score.to_f64().unwrap_or(0f64) / (criteria.count as f64);
                        }
                    }
                }
            });
        }
        // 作业消耗统计信息
        let homework = HomeworkRepository::fetch_homework_by_id(&self.db_pool, schema_name, id).await?;
        summary.homework_info.leaf_count = homework.leaf_count;
        summary.homework_info.leaf_total = homework.leaf_total;
        summary.homework_info.page_total = homework.page_total;
        summary.homework_info.page_count = homework.page_count;
        Ok(summary)
    }
    pub async fn delete_homework(&self, _context: &AuthContext, schema_name: &String, id: &Uuid) -> Result<Homework, String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name).await.map_err(|e| e.to_string())?;
        let deleted_homework = HomeworkRepository::delete_homework(&mut conn, id).await.map_err(|e| e.to_string())?;
        Ok(deleted_homework)
    }
}

/// 学科组题卡消耗情况统计
#[derive(Serialize, Default)]
pub struct CardConsumeStatistics {
    pub subject_group_id: Uuid,
    pub subject_code: String,
    pub subject_name: String,
    pub leaf_total: i64,
    pub page_total: i64,
    pub scanned_leaf_total: i64,
    pub scanned_page_total: i64,
    pub homework_total: i64,
}

#[derive(Serialize, Default)]
pub struct AssembleCardMetricsResponse {
    pub scanned_leaf_total: i64,
    pub scanned_page_total: i64,
    pub leaf_total: i64,
    pub leaf_count: i64,
    pub page_total: i64,
    pub page_count: i64,
    pub homework_total: i64,
}

#[derive(Debug, FromRow, Clone, Serialize, Deserialize)]
pub struct PageHomeworkResult {
    //数据库字段
    pub homework: Homework,
    //联查的学生数据
    pub student_list: Vec<StudentBaseInfo>,
}
