use sqlx::PgPool;
use uuid::Uuid;
use crate::model::homework::homework_feedback::{HomeworkFeedback, HomeworkFeedbackStatus};
use crate::model::PageParams;
use crate::repository::homework::homework_feedback_repository::HomeworkFeedbackRepository;

pub struct HomeworkFeedbackService {
    db: PgPool,
    tenant_name: String,
}


impl HomeworkFeedbackService {
    pub fn new(db: PgPool, tenant_name: String) -> Self {
        Self { db, tenant_name }
    }
    pub async fn create_feedback(&self, homework_id: Uuid, student_id: Uuid, score_id: Option<Uuid>, text: String,) -> anyhow::Result<HomeworkFeedback> {
        HomeworkFeedbackRepository::create_feedback(&self.db, self.tenant_name.as_str(), homework_id, student_id, score_id, text).await
    }
    pub async fn page_feedbacks(&self, homework_ids: Vec<Uuid>, page_params: PageParams, status_list: Vec<HomeworkFeedbackStatus>) -> anyhow::Result<(Vec<HomeworkFeedback>, i64)> {
        HomeworkFeedbackRepository::fetch_page_feedbacks(&self.db, self.tenant_name.as_str(), homework_ids, status_list, &page_params).await
    }
    pub async fn student_feedbacks(&self, homework_id: Uuid, student_id: Uuid) -> anyhow::Result<Vec<HomeworkFeedback>> {
        HomeworkFeedbackRepository::fetch_feedbacks(&self.db, self.tenant_name.as_str(), homework_id, student_id).await
    }
    pub async fn update_feedback(&self, feedback_id: Uuid, text: Option<String>, status: HomeworkFeedbackStatus) -> anyhow::Result<()> {
        HomeworkFeedbackRepository::update_feedback(&self.db, self.tenant_name.as_str(), feedback_id, status, text).await
    }
}
