use crate::config::grading_config::GradingConfig;
use crate::model::grading::grading::{PaperScanPathRequest, ScanQueryParams, EXAM_TYPE_HOMEWORK};
use crate::model::grading::paper_scan_block::PaperScanBlock;
use crate::model::grading::paper_scan_pages::{PaperScanPagesRecord, UpdatePaperScanPages};
use crate::model::grading::paper_scans::{PaperScanPageRecord, PaperScanStatus, UpdatePaperScan};
use crate::model::grading::recognize::{BlockData, BlockValue, PageData, PageRequest, RecognizeRequest, SheetRequest, SheetResponse};
use crate::model::paper::paper_cache::{AnswerBlockScoringCriteria, PaperContentData};
use crate::model::score::Score;
use crate::repository::homework_papers::homework_papers_repository::HomeworkPapersRepository;
use crate::repository::homework_students::homework_students_repository::HomeworkStudentsRepository;
use crate::repository::paper_scan_block::paper_scan_block_repository::PaperScansBlockRepository;
use crate::repository::paper_scan_page::paper_scan_page_repository::PaperScanPageRepository;
use crate::repository::paper_scans::paper_scans_repository::PaperScansRepository;
use crate::repository::score::score_detail_repository;
use crate::repository::score::score_repository::{batch_delete_score_blocks, get_scores_with_block_ids, rebind_student_to_score, ScoreRepository};
use crate::service::grading::grading_service::GradingService;
use crate::service::homework::homework_students_service::HomeworkStudentsService;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::service::storage::storage_service::UploadOptions;
use crate::service::storage::FileInfo;
use crate::service::student::student_service::StudentService;
use crate::utils::http_client::{parse_json_response, post_json};
use crate::utils::schema::connect_with_schema;
use crate::web_server::AppState;
use anyhow::{anyhow, Context};
use axum::extract::Multipart;
use chrono::{DateTime, Local, Utc};
use serde::Serialize;
use serde_json::Value;
use sqlx::Acquire;
use sqlx::PgPool;
use std::collections::{HashMap, HashSet};
use std::path::Path;
use tracing::log::error;
use tracing::{debug, info};
use uuid::Uuid;

#[derive(Clone)]
pub struct PaperScansService {
    pool: PgPool,
    student_service: StudentService,
}

/// 定义分割题块处理结果实体
#[derive(Clone)]
pub struct PaperScansSplitSheetsAnswerBlockResponse {
    pub paper_id: Uuid,                         // 纸张ID
    pub exam_id: Uuid,                          // 考试或作业 ID ,要结合 exam_type 取值来判断 (homework:作业、exam: 考试) 后面定枚举
    pub exam_type: String,                      //(homework:作业、exam: 考试) 后面定枚举
    pub student_id: Option<Uuid>,               // 学生ID
    pub paper_scan_blocks: Vec<PaperScanBlock>, // 拆解后存放题块信息
    pub scores: Vec<Score>,                     // 拆解后存放分数块
}

/// 定义纸张批次号明细Vo
#[derive(Debug, Clone, Serialize)]
pub struct PaperScansBatchInfoVo {
    pub exam_id: Uuid,
    pub total_scan_count: i64,      //扫描总数
    pub processing_count: i64,      //处理中
    pub total_unbound_count: i64,   // 总未绑定学号张数
    pub total_duplicate_count: i64, // 总重复张数
    pub error_scan_count: i64,      //扫描异常
    pub completed_count: i64,       //处理完成
    pub batch_no: String,
    pub created_at: Option<DateTime<Utc>>,
    pub status: PaperScanStatus,
}

impl PaperScansBatchInfoVo {
    pub fn new(exam_id: Uuid, created_at: Option<DateTime<Utc>>, status: PaperScanStatus) -> Self {
        Self {
            exam_id,
            total_scan_count: 0,
            processing_count: 0,
            total_unbound_count: 0,
            total_duplicate_count: 0,
            error_scan_count: 0,
            completed_count: 0,
            batch_no: "".to_string(),
            created_at,
            status,
        }
    }
}

/// 定义纸张扫描总览信息
#[derive(Debug, Clone, Serialize, Default)]
pub struct PaperScansStatisticsResponse {
    pub total_scan_count: i64,      //扫描总数
    pub processing_count: i64,      //处理中
    pub total_unbound_count: i64,   // 总未绑定学号张数
    pub total_duplicate_count: i64, // 总重复张数
    pub error_scan_count: i64,      //扫描异常
    pub completed_count: i64,       //处理完成
}

/// 扫描纸张服务类
/// 作者: 萧达光
impl PaperScansService {
    pub fn new(pool: PgPool, student_service: StudentService) -> Self {
        Self { pool, student_service }
    }

    /// 重新识别纸张内容
    /// 作者: 萧达光
    /// TODO:暂不考虑跨纸张情况
    /// 说明: 该操作会联动 (paper_scans、paper_scan_pages、score_blocks、score_details、scores、paper_scan_block) 这几个表中的数据
    pub async fn batch_recognize_scanned_pages(
        &self,
        state: AppState,
        bucket: String,
        config: GradingConfig,
        schema_name: &str,
        exam_id: Uuid,
        batch_number: Option<String>,
        pages_ids: Option<Vec<Uuid>>,
    ) -> anyhow::Result<()> {
        let mut paper_list = HomeworkPapersRepository::fetch_paper_by_homework_id(&self.pool, schema_name, exam_id).await.map_err(|e| anyhow!(e))?;

        // 查询试卷信息
        if paper_list.is_empty() || paper_list.len() > 1 {
            return Err(anyhow!("试卷列表不能为空或只一场作业只允许一绑定张试卷!"));
        }
        // 根据试卷拿到答题卡总页码
        let paper = paper_list.remove(0);
        let paper_content = paper.paper_content.0;
        let page_total = paper_content.answer_card.page_total;
        // 1.根据条件查询对应的纸张或页数据
        let leaf_ids = if let Some(pages_ids) = pages_ids {
            Some(PaperScanPageRepository::fetch_leaf_ids_scan_pages(&self.pool, schema_name, pages_ids).await?)
        } else { None };
        let pages = PaperScanPageRepository::fetch_paper_scan_pages(&self.pool, schema_name, exam_id, batch_number, leaf_ids).await?;
        let student_ids: Vec<_> = pages.iter().filter_map(|p| p.student_id).collect();
        if pages.is_empty() {
            return Err(anyhow!("没有找到对应的纸张页面信息"));
        }

        // 转换成纸张ID
        let mut leaf_map: HashMap<Uuid, Vec<_>> = pages.iter().fold(HashMap::new(), |mut map, page| {
            map.entry(page.paper_scan_id).or_default().push((page.paper_scan_pages_id, page.paper_scan_page_file_url.clone()));
            map
        });
        // 重置纸张状态为处理中
        let leaf_ids: Vec<Uuid> = leaf_map.keys().cloned().collect();
        self.update_paper_scans_and_page_status(schema_name, leaf_ids.clone(), PaperScanStatus::Undistributed, None).await?;
        let leafs: Vec<_> = leaf_map.iter().map(|(k, v)| (k.clone(), v.clone())).collect();

        // 2.调用重新识别题卡服务、重新识别题卡信息
        let response_sheets = match self.recognize_paper_scans(bucket.clone(), config.clone(), leafs.clone(), false, &paper_content).await {
            Ok(sheets) => sheets,
            Err(e) => {
                let error_message = format!("调用阅卷识别服务异常: {}", e);
                error!("{}", error_message);
                // 如果出现异常、更新当前纸张状态信息
                let _ = self.update_paper_scans_and_page_status(schema_name, leaf_ids, PaperScanStatus::Error, Some(error_message)).await;
                // 继续下一组处理
                return Err(e);
            }
        };
        debug!("sheets:{:?}", response_sheets);
        for response_sheet in response_sheets {
            // 3.根据识别结果,重建对应的关联关系并且重构题块打分点等信息
            if let Some(items) = leaf_map.remove(&response_sheet.uuid) {
                let page_ids = items.into_iter().map(|item| item.0).collect();
                self.rebuild_question_blocks_with_scores(state.clone(), schema_name, &exam_id, &paper_content, response_sheet.uuid, page_ids, response_sheet)
                    .await?;
            }
        }
        for student_id in student_ids {
            let homework_student_service = HomeworkStudentsService::new(self.pool.clone());
            homework_student_service.update_homework_student_status(schema_name, exam_id, student_id, page_total).await?;
            self.mark_student_page_as_duplicate(schema_name, exam_id, student_id, page_total).await?;
        }
        Ok(())
    }
    ///根据识别结果,重建对应的关联关系并且重构题块打分点等信息
    pub async fn rebuild_question_blocks_with_scores(
        &self,
        state: AppState,
        schema_name: &str,
        exam_id: &Uuid,
        paper_content: &PaperContentData,
        leaf_id: Uuid,
        page_ids: Vec<Uuid>,
        sheet: SheetResponse,
    ) -> anyhow::Result<()> {
        let service = GradingService::new(state.db.clone());
        let mut conn = connect_with_schema(&self.pool, schema_name).await?;
        let scan_blocks = PaperScansBlockRepository::get_blocks_by_page_ids(&self.pool, schema_name, page_ids.clone()).await?;
        let scan_block_ids: Vec<Uuid> = scan_blocks.iter().map(|b| b.id).collect();
        // TODO 目前这里存在一个 Bug 就是当删除页的时候，如果不是完整删除所有页，就会导致一个打分点存在多个页里，后面要解决这个问题
        let scores = get_scores_with_block_ids(&self.pool, schema_name, scan_block_ids).await?;
        let score_ids: Vec<Uuid> = scores.iter().map(|score| score.id).collect();
        //1. 重置纸张和页面状态
        let _rows_affected = PaperScansRepository::batch_update_paper_scans_status(&self.pool, schema_name, PaperScanStatus::Undistributed, vec![leaf_id]).await?;
        let mut tx = conn.begin().await?;

        //1.批量删除相关的题块表(paper_scan_block)中的记录
        let _rows_affected = PaperScansBlockRepository::batch_delete_paper_scan_block(&mut tx, &page_ids).await?;
        //2.批量删除相关的打分明细表(score_details) 中的记录
        let _rows_affected = score_detail_repository::batch_delete_score_details(&mut tx, &score_ids).await?;
        //3.批量删除相关的打分与题块中间关系表 (score_blocks) 中的记录
        let _rows_affected = batch_delete_score_blocks(&mut tx, &score_ids).await?;
        //4.批量删除相关的打分表(scores) 中的记录
        let _rows_affected = ScoreRepository::batch_delete_score(&mut tx, &score_ids).await?;
        tx.commit().await?;

        // 5. 分割题块
        let paper_scans_split_sheets_answer_block_response = match self
            .split_sheets_answer_block(schema_name, &paper_content, sheet, leaf_id, exam_id.clone(), EXAM_TYPE_HOMEWORK.to_string())
            .await
        {
            Ok(response) => response,
            Err(error) => {
                let error_message = format!("调用题块分割服务异常: {}", error);
                error!("{}", error_message.clone());
                // 如果出现异常、更新当前纸张及页面状态信息
                let _ = self
                    .update_paper_scans_and_page_status(schema_name, vec![leaf_id.clone()], PaperScanStatus::Error, Some(error_message.clone()))
                    .await;

                return Err(anyhow!(error_message));
            }
        };

        // 6 根据阅卷评分标准调整相关分数
        if let Err(e) = service
            .smart_dispatch_grading_score(schema_name, state.task_queue.clone(), paper_scans_split_sheets_answer_block_response, paper_content.clone())
            .await
        {
            error!("smart_dispatch_grading_score: {}", e);
        }

        Ok(())
    }
    ///重绑学生号与纸张对应关系
    /// TODO:目前不跨纸张问题
    pub async fn rebind_student_to_leaf(&self, schema_name: &str, exam_id: Uuid, page_ids: Vec<Uuid>, source_student_id: Option<Uuid>, target_student_id: Uuid) -> anyhow::Result<()> {
        let homework_student_service = HomeworkStudentsService::new(self.pool.clone());
        let total_page = HomeworkPapersRepository::get_homework_total_page(&self.pool, schema_name, exam_id).await.map_err(|e| anyhow!(e))?;
        //1.检验当前学生是否在当前这场考试
        let homework_student = HomeworkStudentsRepository::get_student_by_homework_id_and_student_id(&self.pool, schema_name, &exam_id, &target_student_id)
            .await
            .map_err(|e| anyhow!("{}", e))?;
        if homework_student.is_none() {
            return Err(anyhow!("该学生没有参与本场作业或考试!"));
        }

        //2.根据纸张页ID和考试ID 查询对应纸张页面记录
        let pages = PaperScanPageRepository::fetch_paper_scan_pages_by_page_id(&self.pool, schema_name, exam_id, page_ids.clone()).await?;
        if pages.is_empty() {
            return Err(anyhow!("没有找到对应的页面信息"));
        }

        // 转换成纸张ID
        let leaf_ids: HashSet<Uuid> = HashSet::from_iter(pages.iter().map(|p| p.paper_scan_id));
        let leaf_ids: Vec<Uuid> = leaf_ids.into_iter().collect();

        if let Some(student) = homework_student {
            PaperScansRepository::rebind_student_to_leaf(&self.pool, schema_name, exam_id, target_student_id, student.student_number, leaf_ids).await?;
        }

        //3.更新当前考试下所有学生状态
        if let Some(source_student_id) = source_student_id {
            homework_student_service.update_homework_student_status(schema_name, exam_id, source_student_id, total_page).await?;
        }
        homework_student_service.update_homework_student_status(schema_name, exam_id, target_student_id, total_page).await?;

        //4.检测更新当前纸张页重复状态
        if let Some(student_id) = source_student_id {
            self.mark_student_page_as_duplicate(schema_name, exam_id, student_id, total_page).await?;
        }
        self.mark_student_page_as_duplicate(schema_name, exam_id, target_student_id, total_page).await?;

        //5.更新纸张页对应的学生ID 绑定关系 (paper_scans -> scan_page -> scan_blocks -> scores)
        let scan_blocks = PaperScansBlockRepository::get_blocks_by_page_ids(&self.pool, schema_name, page_ids.clone()).await?;
        let block_ids = scan_blocks.iter().map(|s| s.id).collect();
        let scores = get_scores_with_block_ids(&self.pool, schema_name, block_ids).await?;
        //6.更新打分点对应的绑定学生关系 (scores)
        let mut row_affected = 0;
        if !scores.is_empty() {
            let score_ids: Vec<Uuid> = scores.iter().map(|s| s.id).collect();
            row_affected += rebind_student_to_score(&self.pool, schema_name, target_student_id, score_ids).await?;
        }
        info!("rebind row_affected scores count: {}", row_affected);
        Ok(())
    }
    pub async fn set_page_blank(&self, schema_name: &str, exam_id: Uuid, page_id: Uuid) -> anyhow::Result<()> {
        // 修改扫描页为空白页
        PaperScanPageRepository::update_scan_page_blank(&self.pool, schema_name, page_id).await?;
        // 刷新纸张状态
        let total_page = HomeworkPapersRepository::get_homework_total_page(&self.pool, schema_name, exam_id).await.map_err(|e| anyhow!(e))?;
        let page_record = PaperScanPageRepository::fetch_paper_scan_pages_by_page_id(&self.pool, schema_name, exam_id, vec![page_id]).await?.remove(0);
        // 刷新考生状态
        if let Some(student_id) = page_record.student_id {
            let homework_student_service = HomeworkStudentsService::new(self.pool.clone());
            homework_student_service.update_homework_student_status(schema_name, exam_id, student_id, total_page).await?;
            self.mark_student_page_as_duplicate(schema_name, exam_id, student_id, total_page).await?;
        }
        Ok(())
    }

    /// 校验是否存在重复页面并且更新状态
    pub async fn mark_student_page_as_duplicate(&self, schema_name: &str, exam_id: Uuid, student_id: Uuid, total_page: i32) -> anyhow::Result<()> {
        let pages = PaperScanPageRepository::fetch_scan_pages_by_student_id(&self.pool, schema_name, student_id, exam_id).await?;
        let page_numbers = pages.iter().map(|p| p.page_num).collect();
        let leaf_ids = pages.iter().map(|p| p.paper_scan_id).collect();
        let page_ids = pages.iter().map(|p| p.paper_scan_pages_id).collect();
        let (flag, duplicate_page_number, _) = self.validate_page_numbers(total_page, page_numbers);

        if flag {
            PaperScansRepository::mark_paper_scan_as_status_done(&self.pool, schema_name, leaf_ids, page_ids).await?;
        } else {
            if duplicate_page_number.len() > 0 {
                let duplicate_pages: Vec<PaperScanPagesRecord> = pages.into_iter().filter(|record| duplicate_page_number.contains(&record.page_num)).collect();
                let duplicate_leaf_ids: Vec<Uuid> = duplicate_pages.iter().map(|record| record.paper_scan_id).collect();
                let duplicate_page_ids: Vec<Uuid> = duplicate_pages.iter().map(|record| record.paper_scan_pages_id).collect();
                PaperScansRepository::mark_paper_scan_as_duplicate_status(&self.pool, schema_name, duplicate_leaf_ids).await?;
                PaperScanPageRepository::mark_page_as_duplicate_status(&self.pool, schema_name, duplicate_page_ids).await?;
            }
        }

        Ok(())
    }

    /// 查询当前考试扫描总览
    pub async fn get_statistics(&self, schema_name: &String, exam_id: Uuid) -> anyhow::Result<PaperScansStatisticsResponse> {
        // 切换到租户域
        let mut response = PaperScansStatisticsResponse::default();
        let status_summary_records = PaperScansRepository::calculate_scan_statistics(&self.pool, schema_name, exam_id).await?;

        for item in status_summary_records {
            match item.status {
                PaperScanStatus::Undistributed => response.processing_count = item.total,
                PaperScanStatus::Unbound => {
                    response.total_unbound_count += item.total;
                    response.error_scan_count += item.total;
                }
                PaperScanStatus::Duplicate => {
                    response.total_duplicate_count += item.total;
                    response.error_scan_count += item.total;
                }
                PaperScanStatus::Error => response.error_scan_count += item.total,
                PaperScanStatus::Done => response.completed_count += item.total,
            }
            response.total_scan_count += item.total;
        }

        Ok(response)
    }

    /// 获取扫描批次号列表
    pub async fn get_paper_scan_batch_number_list(&self, schema_name: &str, params: &ScanQueryParams) -> anyhow::Result<Vec<PaperScansBatchInfoVo>> {
        // 全部
        let mut all_item = PaperScansBatchInfoVo::new(params.exam_id.unwrap(), None, PaperScanStatus::Undistributed);

        if let Some(exam_id) = params.exam_id {
            // 根据考试/作业ID 查询上传纸张图片信息
            let paper_scans = PaperScansRepository::find_paper_scans_pages_by_exam_id(&self.pool, schema_name, exam_id).await?;

            // 根据当前纸张信息分组统计明细并封装到列表中
            let mut batch_info_map: HashMap<String, PaperScansBatchInfoVo> = HashMap::new();

            for item in paper_scans {
                let batch_info = batch_info_map
                    .entry(item.batch_no.clone())
                    .or_insert_with(|| PaperScansBatchInfoVo::new(item.exam_id, Some(item.created_at), PaperScanStatus::Undistributed));

                match item.status {
                    PaperScanStatus::Undistributed => {
                        batch_info.processing_count += 1;
                        all_item.processing_count += 1;
                    }
                    PaperScanStatus::Unbound => {
                        batch_info.total_unbound_count += 1;
                        all_item.total_unbound_count += 1;
                    }
                    PaperScanStatus::Duplicate => {
                        batch_info.total_duplicate_count += 1;
                        all_item.total_duplicate_count += 1;
                    }
                    PaperScanStatus::Error => {
                        batch_info.error_scan_count += 1;
                        all_item.error_scan_count += 1;
                    }
                    PaperScanStatus::Done => {
                        batch_info.completed_count += 1;
                        all_item.completed_count += 1;
                    }
                }
                batch_info.batch_no = item.batch_no;
                batch_info.total_scan_count += 1;
                all_item.total_scan_count += 1;
                all_item.created_at = Some(item.created_at);
            }

            // 将 map 展平为数组
            let mut result: Vec<PaperScansBatchInfoVo> = batch_info_map.into_values().collect();
            // 根据日期降序
            result.sort_by(|a, b| b.created_at.cmp(&a.created_at));
            result.insert(0, all_item);

            // 聚合状态显示
            for item in result.iter_mut() {
                if item.total_unbound_count > 0 || item.total_duplicate_count > 0 || item.error_scan_count > 0 {
                    item.status = PaperScanStatus::Error;
                } else if item.processing_count > 0 {
                    item.status = PaperScanStatus::Undistributed;
                } else {
                    item.status = PaperScanStatus::Done;
                }
            }

            Ok(result)
        } else {
            Ok(vec![])
        }
    }

    /// 判断当前学号是否合法
    fn is_valid_student_number(&self, student_number: &str, max_len: usize) -> bool {
        !student_number.is_empty()
            && student_number.len() <= max_len  // 限制最长10位
            && student_number.parse::<u64>().is_ok()
    }

    /// 批量更新试卷纸张状态与页页面状态
    pub async fn update_paper_scans_and_page_status(&self, tenant_name: &str, leaf_ids: Vec<Uuid>, status: PaperScanStatus, abnormal_reason: Option<String>) -> anyhow::Result<()> {
        // 1. 获取当前租户信息并切换连接资源到对应租户
        PaperScansRepository::update_paper_scans_status(&self.pool, tenant_name, leaf_ids.clone(), &status, &abnormal_reason).await?;
        PaperScanPageRepository::update_paper_scan_page_status(&self.pool, tenant_name, leaf_ids.clone(), &status, &abnormal_reason).await?;
        Ok(())
    }
    /// 批量更新试卷纸张状态与页页面信息
    pub async fn update_paper_scans_and_page(&self, tenant_name: &str, update_paper_scan: UpdatePaperScan) -> anyhow::Result<()> {
        // 1. 获取当前租户信息并切换连接资源到对应租户
        PaperScansRepository::update_paper_scans(&self.pool, tenant_name, update_paper_scan.clone()).await?;

        let pages = update_paper_scan.scan_pages.clone();

        // 将异常状态回写到页码中
        for page in pages {
            PaperScanPageRepository::update_paper_scan_page(
                &self.pool,
                tenant_name,
                UpdatePaperScanPages::new(
                    page.id,
                    page.student_id,
                    page.student_number,
                    page.paper_scan_id,
                    page.page_num,
                    page.rectify_url,
                    page.duplicate_num,
                    page.blank_num,
                    page.is_abnormal,
                    page.is_blank,
                    page.is_abnormal,
                    page.abnormal_reason,
                    page.result,
                ),
            )
                .await?;
        }

        Ok(())
    }
    /// 更新试卷纸张扫描状态
    pub async fn update_paper_scans(&self, tenant_name: &str, update_paper_scan: UpdatePaperScan) -> anyhow::Result<()> {
        // 1. 获取当前租户信息并切换连接资源到对应租户
        PaperScansRepository::update_paper_scans(&self.pool, tenant_name, update_paper_scan).await?;
        Ok(())
    }

    /// 分割纸张，将题块信息拆分存放
    pub async fn split_sheets_answer_block(
        &self,
        schema_name: &str,
        paper_content: &PaperContentData,
        sheet: SheetResponse,
        leaf_id: Uuid,
        exam_id: Uuid,
        exam_type: String,
    ) -> anyhow::Result<PaperScansSplitSheetsAnswerBlockResponse> {
        // 1. 获取当前租户信息并切换连接资源到对应租户
        let homework_student_service = HomeworkStudentsService::new(self.pool.clone());
        let mut paper_scan_blocks: Vec<PaperScanBlock> = Vec::new();
        let mut scores: Vec<Score> = Vec::new();
        let mut student_id: Option<Uuid> = None;
        // 取出当前试卷答题组块与评分标准关联信息
        let answer_block_group_scoring_criteria_list = paper_content.answer_card.answer_block_group_scoring_criteria_list.clone();

        // 2. 循环遍历该纸张信息
        let paper_scan_id = sheet.uuid;
        let _exam_type = exam_type.as_str();
        let exam_id = exam_id;
        let duplicate_num = 0;
        let mut blank_num = 0;
        let page_total = paper_content.answer_card.page_total;
        let mut global_is_abnormal = false;
        let mut global_duplicate_count = 0; // 重复数

        let result: Value = serde_json::to_value(&sheet).unwrap_or_default(); // 存放解释内容

        // 判断是否是一个合法的学号
        let student_number: Option<String>;
        let is_valid_number = self.is_valid_student_number(sheet.number.as_str(), 20);

        if is_valid_number {
            // 查询学生信息
            let student = self.student_service.get_student_by_student_number(schema_name, sheet.number.as_str()).await?;
            if let Some(student) = student {
                student_id = Some(student.id);
                student_number = Some(student.student_number.clone());
            } else {
                student_number = Some(sheet.number);
            }
        } else {
            student_number = None;
        }

        // 2.1提取页码信息
        let mut leaf_scan_blocks: Vec<(BlockData, Uuid)> = Vec::new();
        let mut page_id_set = HashSet::<Uuid>::new();
        for page in sheet.page_containers.clone() {
            let page_id = page.page_id;
            let mut is_duplicate = false;
            let mut is_blank = false;
            let mut is_abnormal = false;
            let mut abnormal_reason: Option<String> = None;
            let mut page_num = -1;
            let mut rectify_url: Option<String> = None;
            let duplicate_pages: Vec<PaperScanPagesRecord>;

            // 处理页面信息提取分割块
            match page.page_data {
                PageData::Value(value) => {
                    info!("value.blocks {:#?}", value.blocks.len());
                    value.blocks.into_iter().for_each(|v| {
                        leaf_scan_blocks.push((v, page_id));
                    });
                    // 更新状态值
                    is_blank = false;
                    is_abnormal = false;
                    abnormal_reason = None;
                    page_num = value.page as i32;
                    rectify_url = Some(value.norm_card_url.clone());

                    if let Some(student_id) = student_id {
                        // 检测当前页是否重复
                        duplicate_pages = PaperScanPageRepository::fetch_duplicate_paper_scan_pages(&self.pool, schema_name, student_id, exam_id, page_num).await?;

                        if duplicate_pages.len() > 0 {
                            global_duplicate_count += duplicate_pages.len() as i32;
                            is_duplicate = true;
                            let duplicate_leaf_ids: Vec<Uuid> = duplicate_pages.iter().map(|p| p.paper_scan_id).collect();
                            let duplicate_page_ids: Vec<Uuid> = duplicate_pages.iter().map(|p| p.paper_scan_pages_id).collect();
                            // 更新重复页面状态
                            PaperScanPageRepository::mark_page_as_duplicate_status(&self.pool, schema_name, duplicate_page_ids.clone()).await?;
                            // 更新页所属纸张状态也标记为重复

                            PaperScansRepository::mark_paper_scan_as_duplicate_status(&self.pool, schema_name, duplicate_leaf_ids.clone()).await?;
                        }
                        // 更新考生状态
                        homework_student_service.update_homework_student_status(schema_name, exam_id, student_id, page_total).await?;
                    }
                }

                PageData::Blank => {
                    blank_num += 1;
                    is_blank = true;
                }
                PageData::Error(error) => {
                    is_abnormal = true;
                    global_is_abnormal = true;
                    abnormal_reason = Some(error.to_string());
                }
            };
            // 更新页面条目信息
            if page_id_set.contains(&page_id) {
                let mut scan_page = PaperScanPageRepository::fetch_scan_page_by_id(&self.pool, schema_name, page_id).await?;
                scan_page.id = Uuid::new_v4();
                scan_page.rectify_url = rectify_url;
                scan_page.page_num = page_num;
                scan_page.is_duplicate = is_duplicate;
                scan_page.is_blank = is_blank;
                scan_page.is_abnormal = is_abnormal;
                scan_page.abnormal_reason = abnormal_reason;
                scan_page.result = Some(result.clone());
                PaperScanPageRepository::save(&self.pool, schema_name, scan_page).await?;
            } else {
                page_id_set.insert(page_id);
                PaperScanPageRepository::update_paper_scan_page(
                    &self.pool,
                    schema_name,
                    UpdatePaperScanPages::new(
                        page_id,
                        student_id,
                        student_number.clone(),
                        paper_scan_id,
                        Some(page_num),
                        rectify_url,
                        duplicate_num,
                        blank_num,
                        is_duplicate,
                        is_blank,
                        is_abnormal,
                        abnormal_reason,
                        Some(result.clone()),
                    ),
                )
                    .await?;
            }
        }

        // 按纸张分割并保存题块与打分点信息
        let (batch_blocks, batch_scores) = self
            .save_blocks_with_scores(schema_name, student_id, leaf_scan_blocks, answer_block_group_scoring_criteria_list.clone())
            .await?;
        paper_scan_blocks.extend(batch_blocks);
        scores.extend(batch_scores);

        // 2.2 统一更新纸张信息
        let mut status: PaperScanStatus = PaperScanStatus::Done;

        if global_is_abnormal {
            status = PaperScanStatus::Error;
        }  else if global_duplicate_count > 0 {
            status = PaperScanStatus::Duplicate;
        } else if student_id.is_none() {
            status = PaperScanStatus::Unbound;
        }

        PaperScansRepository::update_paper_scans(
            &self.pool,
            schema_name,
            UpdatePaperScan::new(
                paper_scan_id,
                exam_type.clone(),
                student_id,
                student_number,
                status,
                Some(result.clone()),
                global_duplicate_count,
                blank_num,
                global_is_abnormal,
                Some(result.clone().to_string()),
                Local::now().into(),
                vec![],
            ),
        )
            .await?;

        Ok(PaperScansSplitSheetsAnswerBlockResponse {
            paper_id: leaf_id,
            exam_id,
            exam_type: exam_type.clone(),
            student_id,
            paper_scan_blocks,
            scores,
        })
    }

    /// 保存题目分块及对应分数信息
    pub async fn save_blocks_with_scores(
        &self,
        tenant_name: &str,
        student_id: Option<Uuid>,
        blocks: Vec<(BlockData, Uuid)>,
        answer_block_group_scoring_criteria_list: Vec<AnswerBlockScoringCriteria>,
    ) -> anyhow::Result<(Vec<PaperScanBlock>, Vec<Score>)> {
        // 分组题块与评分转换成key,value 的方式
        // 获取criteria_id ->
        let mut criteria_block_groups_map: HashMap<Uuid, Vec<Uuid>> = HashMap::new();
        for item in answer_block_group_scoring_criteria_list {
            criteria_block_groups_map.entry(item.scoring_criteria_id).or_insert(Vec::new()).push(item.block_group_id);
        }
        // 处理scores表 score_blocks表 scan_blocks表
        let mut batch_blocks = Vec::new();
        let mut batch_scores = Vec::new();
        let mut batch_scores_blocks: Vec<(Uuid, Uuid)> = Vec::new();
        let mut scan_blocks_map: HashMap<Uuid, (BlockValue, Option<String>, i32, Uuid)> = blocks.into_iter().map(|(v, page_id)| (v.block_id, (v.value, v.url, v.serial_number, page_id))).collect();
        for (criteria_id, block_group_ids) in criteria_block_groups_map {
            let score_id = Uuid::new_v4();
            let temp_batch_blocks = block_group_ids
                .into_iter()
                .filter_map(|id| {
                    if let Some(item) = scan_blocks_map.remove(&id) {
                        let block_id = Uuid::new_v4();
                        let answer_content = match item.0 {
                            BlockValue::Objective(c) => Some(c),
                            BlockValue::Objectives(contents) => Some(serde_json::to_string(&contents).unwrap_or_default()),
                            BlockValue::Subjective => None,
                        };
                        Some(PaperScanBlock::new(block_id, item.3, id, item.1, answer_content, item.2))
                    } else {
                        None
                    }
                })
                .collect::<Vec<_>>();
            if temp_batch_blocks.is_empty() {
                continue;
            }
            batch_scores.push(Score::new(score_id, criteria_id, student_id));
            temp_batch_blocks.iter().for_each(|v| {
                batch_scores_blocks.push((score_id, v.id));
            });
            batch_blocks.extend(temp_batch_blocks);
        }

        // 执行批量插入
        let mut conn = connect_with_schema(&self.pool, tenant_name).await.map_err(|e| anyhow!("{}", e.to_string()))?;

        let mut tx = conn.begin().await?;

        info!(
            "batch_blocks: {} batch_scores: {} batch_scores_blocks: {}",
            batch_blocks.len(),
            batch_scores.len(),
            batch_scores_blocks.len()
        );
        if !batch_blocks.is_empty() {
            PaperScansBlockRepository::batch_insert_blocks_with_unnest(&mut tx, batch_blocks.clone()).await?;
        }
        if !batch_scores.is_empty() {
            ScoreRepository::batch_insert_scores_with_unnest(&mut tx, &batch_scores).await?;
        }
        if !batch_scores_blocks.is_empty() {
            ScoreRepository::batch_insert_score_blocks(&mut tx, &batch_scores_blocks).await?;
        }
        tx.commit().await?;
        Ok((batch_blocks, batch_scores))
    }

    /// 将当前试卷扫描纸张文件上传到 Minio 服务端
    pub async fn upload_paper_scans_to_minio(&self, state: AppState, params: PaperScanPathRequest, mut payload: Multipart) -> anyhow::Result<Vec<FileInfo>, String> {
        let mut uploaded_files: Vec<FileInfo> = Vec::new();
        while let Some(field) = payload.next_field().await.map_err(|e| format!("Failed reading multipart field: {}", e))? {
            let field_name = field.name().unwrap_or_default().to_string();
            if field_name == "file" {
                // 处理文件字段
                if let Some(file_name) = field.file_name().map(|s| s.to_string()) {
                    let file_id = Uuid::new_v4();
                    let file_name_key = format!("{}_{}", file_id, file_name);
                    let _content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
                    let prefix = format!("{}/{}/{}/{}/{}/{}", "tenants", params.tenant_name, "grading", "paper_scans", params.exam_id, params.batch_no);
                    let bytes = field.bytes().await.map_err(|e| format!("Failed to read file field: {}", e))?;

                    // 上传到 MinIO
                    let files = state
                        .storage_service
                        .upload(
                            &file_name_key,
                            bytes,
                            UploadOptions {
                                preserve_filename: false,
                                prefix: Some(prefix),
                            },
                        )
                        .await
                        .map_err(|e| format!("Failed to upload file to MinIO: {}", e))?;

                    uploaded_files.push(files);
                }
            }
        }
        Ok(uploaded_files)
    }

    /// 从文件名解析页面类型 (1=正面, 2=背面)
    /// 支持格式: Doc1754030229_17.jpg (正面) 或 Doc1754030229_-17.jpg (背面)
    pub fn parse_page_type_from_filename(&self, filename: &str) -> anyhow::Result<i32> {
        let path = Path::new(filename);
        let stem = path.file_stem().and_then(|s| s.to_str()).context("Invalid filename format: no stem")?;

        // 提取下划线后的数字部分
        let num_str = stem.rsplit('_').next().context("Filename must contain '_' followed by number")?;

        let page_num: i32 = num_str.parse().context("Failed to parse number after '_'")?;

        match page_num {
            n if n > 0 => Ok(1),                              // 正数 -> 正面
            n if n < 0 => Ok(2),                              // 负数 -> 背面
            _ => anyhow::bail!("Page number cannot be zero"), // 0 是非法值
        }
    }

    /// 识别纸张中的作答信息并且提取
    pub async fn recognize_paper_scans(
        &self,
        bucket: String,
        config: GradingConfig,
        leafs: Vec<(Uuid, Vec<(Uuid, String)>)>,
        double_page_in_one: bool,
        paper_content: &PaperContentData,
    ) -> anyhow::Result<Vec<SheetResponse>> {
        let key = if double_page_in_one { "recognize_double_page_in_one" } else { "recognize" };
        let url = format!("{}/sheet/{}", config.get_endpoint_url(), key);
        info!("url:{}", url);
        let mut sheets: Vec<SheetRequest> = Vec::new();
        for (leaf_id, scan_pages) in leafs {
            let mut sheet: SheetRequest = SheetRequest {
                uuid: leaf_id.to_string(),
                bucket: bucket.clone(),
                pages: vec![],
            };
            for (page_id, url) in scan_pages {
                sheet.pages.push(PageRequest::new(page_id.to_string(), url.clone()));
            }
            sheets.push(sheet);
        }

        let answer_card = paper_content.answer_card.clone();
        let payload: RecognizeRequest = RecognizeRequest { mark: answer_card, sheets };
        debug!("{:?}", &payload);
        let response = post_json(url.as_str(), &payload).await?;
        if response.status() != 200 {
            return Err(anyhow!("{}", response.text().await?));
        }

        let result: Vec<SheetResponse> = parse_json_response(response).await?;

        Ok(result)
    }

    /// 根据考试或批次号,批量物理清除扫描纸张关联的所有记录(同时会把所有关联表记录一同清除)
    ///作者:萧达光
    pub async fn purge_batch_scans(&self, schema_name: &str, exam_id: Uuid, batch_number: Option<String>) -> anyhow::Result<()> {
        //根据考试ID和批次号信息查询纸张
        let paper_scans_pages = PaperScansRepository::find_all_paper_scans_pages_by_exam_id_batch_number(&self.pool, schema_name, exam_id, batch_number).await?;
        let pages_ids: HashSet<Uuid> = paper_scans_pages.iter().map(|p| p.page_id).collect();
        let pages_ids: Vec<Uuid> = pages_ids.into_iter().collect();
        let paper_scan_ids: HashSet<Uuid> = paper_scans_pages.iter().map(|p| p.id).collect();
        let paper_scan_ids: Vec<Uuid> = paper_scan_ids.into_iter().collect();
        self.purge_paper_scan_page(schema_name, exam_id, Some(paper_scan_ids.clone()), pages_ids).await?;

        Ok(())
    }
    /// 根据 pages_ids ,物理删除指定页面信息(同时会把所有关联表记录一同清除)
    pub async fn delete_paper_scan_page(&self, schema_name: &str, exam_id: Uuid, pages_ids: Vec<Uuid>) -> anyhow::Result<()> {
        let mut paper_scan_ids: HashSet<Uuid> = HashSet::new();
        //根据考试ID和批次号信息查询纸张
        let paper_scans_pages = PaperScansRepository::find_all_paper_scans_pages_by_exam_id_batch_number(&self.pool, schema_name, exam_id, None).await?;
        let paper_scans_map: HashMap<Uuid, Vec<Uuid>> = paper_scans_pages.iter().fold(HashMap::new(), |mut map, p| {
            map.entry(p.id).or_insert_with(Vec::new).push(p.page_id);
            map
        });

        // 处理是否要完整删除一张纸条件
        for (key, value) in paper_scans_map {
            //只有完整包含一张纸所需的ID才会压进 paper_scan_ids 集合中
            if value.iter().any(|item| pages_ids.contains(item)) {
                paper_scan_ids.insert(key);
            }
        }

        // 调用批量删除逻辑
        let paper_scan_ids = paper_scan_ids.into_iter().collect();
        self.purge_paper_scan_page(schema_name, exam_id, Some(paper_scan_ids), pages_ids).await?;

        Ok(())
    }

    /// 根据 paper_scan_ids 与 pages_ids ,物理删除指定页面信息(同时会把所有关联表记录一同清除)
    /// 作者:萧达光
    pub async fn purge_paper_scan_page(&self, schema_name: &str, exam_id: Uuid, paper_scan_ids: Option<Vec<Uuid>>, pages_ids: Vec<Uuid>) -> anyhow::Result<()> {
        let homework_student_service = HomeworkStudentsService::new(self.pool.clone());
        let homework_paper_service = HomeworkPapersService::new(self.pool.clone(), schema_name.to_string().clone());

        let mut conn = connect_with_schema(&self.pool, schema_name).await.map_err(|e| anyhow!("{}", e.to_string()))?;

        let scan_blocks = PaperScansBlockRepository::get_blocks_by_page_ids(&self.pool, schema_name, pages_ids.clone()).await?;
        let scan_block_ids: Vec<Uuid> = scan_blocks.iter().map(|b| b.id).collect();
        // TODO 目前这里存在一个 Bug 就是当删除页的时候，如果不是完整删除所有页，就会导致一个打分点存在多个页里，后面要解决这个问题
        let scores = get_scores_with_block_ids(&self.pool, schema_name, scan_block_ids).await?;
        let score_ids: Vec<Uuid> = scores.iter().map(|score| score.id).collect();

        let mut tx = conn.begin().await?;
        //1.批量删除相关的题块表(paper_scan_block)中的记录
        let _rows_affected = PaperScansBlockRepository::batch_delete_paper_scan_block(&mut tx, &pages_ids).await?;
        //2.批量删除相关的页码表(paper_scan_pages)中的记录
        let _rows_affected = PaperScanPageRepository::batch_delete_paper_scan_pages(&mut tx, &pages_ids).await?;

        if let Some(paper_scan_ids) = paper_scan_ids {
            if paper_scan_ids.len() > 0 {
                //3.批量删除相关的纸张表(paper_scans)中的记录
                let _rows_affected = PaperScansRepository::batch_delete_paper_scans(&mut tx, &paper_scan_ids).await?;
            }
        }

        //4.批量删除相关的打分明细表(score_details) 中的记录
        let _rows_affected = score_detail_repository::batch_delete_score_details(&mut tx, &score_ids).await?;
        //5.批量删除相关的打分与题块中间关系表 (score_blocks) 中的记录
        let _rows_affected = batch_delete_score_blocks(&mut tx, &score_ids).await?;
        //6.批量删除相关的打分表(scores) 中的记录
        let _rows_affected = ScoreRepository::batch_delete_score(&mut tx, &score_ids).await?;
        tx.commit().await?;

        //查询试卷信息
        let papers = homework_paper_service.get_homework_papers_by_homework_id(exam_id).await.map_err(|e| anyhow!("{}", e.to_string()))?;
        if papers.len() == 0 {
            return Err(anyhow!("没有找到关联的试卷信息"));
        }
        let page_total = papers.first().unwrap().paper_content.answer_card.page_total;

        //7.检查页面是否存在重复页面
        self.batch_check_and_update_duplicate_pages(schema_name, exam_id, page_total).await?;
        //8.更新学生状态
        let homework_students = HomeworkStudentsRepository::fetch_homework_students_by_homework_id(&self.pool, schema_name, &exam_id, None)
            .await
            .map_err(|e| anyhow!("{}", e.to_string()))?;

        for student in homework_students {
            // 更新学生状态
            match homework_student_service.update_homework_student_status(schema_name, exam_id, student.student_id, page_total).await {
                Ok(()) => {} // 明确指定返回类型为`()`
                Err(e) => error!("更新状态失败: {}", e),
            };
        }

        Ok(())
    }

    ///批量检查并标记重复页面状态
    pub async fn batch_check_and_update_duplicate_pages(&self, schema_name: &str, exam_id: Uuid, total_page: i32) -> anyhow::Result<()> {
        // 获取某一场考试的所有纸张与页面信息
        let paper_scans_pages = PaperScansRepository::find_all_paper_scans_pages_by_exam_id_batch_number(&self.pool, schema_name, exam_id, None).await?;

        // 处理所有非空考号的学生，根据学生ID分组页面信息
        let student_pages: HashMap<Uuid, Vec<PaperScanPageRecord>> = paper_scans_pages.iter().fold(HashMap::new(), |mut map, p| {
            if let Some(student_id) = p.student_id {
                map.entry(student_id).or_insert_with(Vec::new).push(p.clone());
            }
            map
        });

        for (_student_id, pages) in student_pages {
            // 获取当前学生所有页码信息
            let page_numbers: Vec<i32> = pages.iter().map(|i| i.page_num).collect::<Vec<i32>>();
            let (flag, _, _) = self.validate_page_numbers(total_page, page_numbers);

            // 统一更新当前学生对应的答题卡状态为完成
            if flag {
                // 重置当前学生的答题卡为正常
                let leaf_ids = pages.iter().map(|p| p.id).collect();
                let page_ids = pages.iter().map(|p| p.page_id).collect();
                PaperScansRepository::mark_paper_scan_as_status_done(&self.pool, schema_name, leaf_ids, page_ids).await?;
            }
        }

        Ok(())
    }

    /// 检查页码列表是否合法
    /// 作者:萧达光
    /// - 参数 `total_pages`: 总页数（从1开始计数）
    /// - 参数 `page_numbers`: 实际页码列表
    /// - 返回值: (是否合法, 重复页码, 缺失页码)
    pub fn validate_page_numbers(&self, total_pages: i32, page_numbers: Vec<i32>) -> (bool, Vec<i32>, Vec<i32>) {
        // 1. 检查页码范围是否有效
        if total_pages <= 0 {
            return (false, vec![], vec![]);
        }

        // 2. 统计每个页码出现的次数
        let mut page_counts = HashMap::new();
        for page in page_numbers {
            if page < 1 || page > total_pages {
                return (false, vec![], vec![]); // 页码越界
            }
            *page_counts.entry(page).or_insert(0) += 1;
        }

        // 3. 找出重复和缺失的页码
        let mut duplicates = Vec::new();
        let mut missing = Vec::new();

        // 检查重复
        for (page, count) in &page_counts {
            if *count > 1 {
                duplicates.push(*page);
            }
        }

        // 检查缺失
        for page in 1..=total_pages {
            if !page_counts.contains_key(&page) {
                missing.push(page);
            }
        }

        // 4. 判断是否完全合法
        let is_valid = duplicates.is_empty() && missing.is_empty();

        (is_valid, duplicates, missing)
    }
}
