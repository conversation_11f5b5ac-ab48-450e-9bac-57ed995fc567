use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::homework::homework::{CreateHomeworkParams, Homework, HomeworkStatistics, PageHomeworkParams, UpdateHomeworkLeafInfo, UpdateHomeworkParams};
use crate::model::PageParams;
use crate::service::homework::vo::HomeworkSummary;
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::http::HeaderMap;
use axum::{
    extract::{Path, State},
    routing::{delete, get, post},
    Json, Router,
};
use serde::Deserialize;
use uuid::Uuid;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getStatistics", post(get_statistics))
        .route("/createHomework", post(create_homework))
        .route("/updateHomework", post(update_homework))
        .route("/updateHomeworkLeafInfo", post(update_homework_leaf_info))
        .route("/pageHomework", post(page_homework))
        .route("/pageStudentHomeworks", post(page_student_homeworks))
        .route("/getHomeworkById/{id}", get(get_homework_by_id))
        .route("/{id}/summary", post(homework_summary))
        .route("/{id}/delete", delete(delete_homework))
}

pub async fn get_statistics(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<HomeworkStatistics>, ApiResponse<()>> {
    state
        .homework_service
        .get_statistics(&tenant_name, context)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn create_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<CreateHomeworkParams>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    state
        .homework_service
        .create_homework(&context, &tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn update_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateHomeworkParams>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    //如果修改了作业状态为已完成，则需要计算该作业所有学生的成绩，并写入数据库
    let UpdateHomeworkParams {
            id,
            homework_name,
            homework_status,
        ..
        } = &params;
    if homework_status== "Done"{
        state.homework_students_service.calculate_student_homework_score(&tenant_name, &id,&None)
            .await
            .map_err(|e| responses::error(e.to_string().as_str(), None))?;
        println!("计算{:?}学生成绩完成",homework_name);
    }
    state
        .homework_service
        .update_homework(&context, &tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn update_homework_leaf_info(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateHomeworkLeafInfo>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    state
        .homework_service
        .update_homework_leaf_info(&context, &tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn page_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageHomeworkParams>,
) -> Result<PaginatedApiResponse<Homework>, PaginatedApiResponse<()>> {
    state
        .homework_service
        .page_all_homework_with_data_filter(&context, &tenant_name, &params.page_params, &params.name, &params.status, &params.subject_group_id)
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))
        .map(|(list, count)| responses::paginated_success(list, params.page_params.get_page(), params.page_params.get_page_size(), count, None))
}
#[derive(Deserialize)]
pub struct StudentHomeworkParam {
    page_params: PageParams,
    name: Option<String>,
    status: Option<String>,
    subject_group_id: Option<Uuid>,
}
pub async fn page_student_homeworks(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    Path(tenant_name): Path<String>,
    Json(params): Json<StudentHomeworkParam>,
) -> Result<PaginatedApiResponse<Homework>, PaginatedApiResponse<()>> {
    state
        .homework_service
        .page_student_homeworks(&context, &tenant_name, &params.page_params,&params.name, &params.status, &params.subject_group_id)
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))
        .map(|(list, count)| responses::paginated_success(list, params.page_params.get_page(), params.page_params.get_page_size(), count, None))
}
/**
 * 作者：朱若彪
 * 说明：根据id查询作业
*/
pub async fn get_homework_by_id(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path((tenant_name, id)): Path<(String, Uuid)>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    state
        .homework_service
        .get_homework_by_id(&context, &tenant_name, id)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}
pub async fn homework_summary(State(state): State<AppState>, Path((tenant_name, id)): Path<(String, Uuid)>) -> Result<ApiResponse<HomeworkSummary>, ApiResponse<()>> {
    let s = state
        .homework_service
        .homework_summary(tenant_name.as_str(), id)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    Ok(ApiResponse::success(s, None))
}
/**
 * 作者：朱若彪
 * 说明：删除作业
*/
pub async fn delete_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path((tenant_name, id)): Path<(String, Uuid)>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    state
        .homework_service
        .delete_homework(&context, &tenant_name, &id)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}
