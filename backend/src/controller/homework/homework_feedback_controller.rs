use axum::{<PERSON><PERSON>, Router};
use axum::extract::{Path, State};
use axum::routing::{post, put};
use serde::Deserialize;
use uuid::Uuid;
use crate::model::homework::homework_feedback::{HomeworkFeedback, HomeworkFeedbackStatus};
use crate::model::PageParams;
use crate::service::homework::homework_feedback_service::HomeworkFeedbackService;
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;

pub fn create_router() -> Router<AppState> {
    Router::new().route("/create", post(create_feedback))
        .route("/pageList", post(page_feedbacks))
        .route("/student_all", post(student_feedbacks))
        .route("/update", put(update_feedback))
}
#[derive(Deserialize)]
struct CreateHomeworkFeedbackParams {
    homework_id: Uuid,
    student_id: Uuid,
    score_id: Option<Uuid>,
    text: String,
}
async fn create_feedback(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<CreateHomeworkFeedbackParams>,
) -> Result<ApiResponse<HomeworkFeedback>, ApiResponse<()>> {
    let service = HomeworkFeedbackService::new(state.db, tenant_name);
    service.create_feedback(params.homework_id, params.student_id, params.score_id, params.text)
        .await.map_err(|e| ApiResponse::error(e.to_string(), None))
        .map(|v| ApiResponse::success(v, None))
}
#[derive(Deserialize)]
struct PageHomeworkFeedbackParams {
    homework_ids: Vec<Uuid>,
    page_params: PageParams,
    status_list: Vec<HomeworkFeedbackStatus>,
}
async fn page_feedbacks(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageHomeworkFeedbackParams>,
) -> Result<PaginatedApiResponse<HomeworkFeedback>, PaginatedApiResponse<()>> {
    let service = HomeworkFeedbackService::new(state.db, tenant_name);
    service.page_feedbacks(params.homework_ids, params.page_params.clone(), params.status_list).await.map_err(|e| PaginatedApiResponse::error(e.to_string(), None))
        .map(|v| PaginatedApiResponse::success(v.0, params.page_params.get_page(), params.page_params.get_page_size(), v.1, None))
}
#[derive(Deserialize)]
struct StudentHomeworkFeedbackParams {
    homework_id: Uuid,
    student_id: Uuid,
}
async fn student_feedbacks(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<StudentHomeworkFeedbackParams>,
) -> Result<ApiResponse<Vec<HomeworkFeedback>>, ApiResponse<()>> {
    let service = HomeworkFeedbackService::new(state.db, tenant_name);
    service.student_feedbacks(params.homework_id, params.student_id)
        .await.map_err(|e| ApiResponse::error(e.to_string(), None))
        .map(|v| ApiResponse::success(v, None))
}
#[derive(Deserialize)]
struct UpdateFeedbackParams {
    id: Uuid,
    text: Option<String>,
    status: HomeworkFeedbackStatus,
}
async fn update_feedback(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateFeedbackParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    let service = HomeworkFeedbackService::new(state.db, tenant_name);
    service.update_feedback(params.id, params.text, params.status)
        .await.map_err(|e| ApiResponse::error(e.to_string(), None))
        .map(|v| ApiResponse::success(v, None))
}