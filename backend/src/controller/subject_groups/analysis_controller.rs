use crate::middleware::auth_middleware::AuthExtractor;
use crate::service::homework::homework_service::CardConsumeStatistics;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::extract::{Path, State};
use axum::routing::get;
use axum::Router;
use tracing::log::error;

pub fn create_router() -> Router<AppState> {
    Router::new().route("/cardConsumeStatistics", get(get_subject_group_card_consume_statistics))
}

// 获取学科组题卡消耗情况统计
pub async fn get_subject_group_card_consume_statistics(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<Vec<CardConsumeStatistics>>, ApiResponse<()>> {
    let result = state.homework_service.card_consume_statistics(&context, tenant_name.as_str()).await.map_err(|e| {
        error!("{}", e);
        ApiResponse::error(e.to_string(), None)
    })?;
    Ok(ApiResponse::success(result, None))
}
