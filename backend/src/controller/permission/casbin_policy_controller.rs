use crate::service::permission::casbin_service::{CasbinPermissionService, PermissionPolicy, RoleRelation};
use crate::middleware::auth_middleware::AuthExtractor;
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::extract::{Path, Query, State};
use axum::http::StatusCode;
use axum::{Json, Router};
use serde::{Deserialize, Serialize};
use sqlx::Row;
use tracing::{error, info, log};
use crate::repository::casbin_policy::casbin_policy_repository::{CasbinPolicyRepository, ScopeRole};

/// Casbin策略管理控制器
pub struct CasbinPolicyController;

/// 策略查询参数
#[derive(Debug, serde::Deserialize)]
pub struct PolicyQueryParams {
    pub tenant_id: Option<String>,
    pub subject: Option<String>,
    pub object: Option<String>,
    pub action: Option<String>,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

/// 权限测试请求
#[derive(Debug, serde::Deserialize)]
pub struct TestPermissionRequest {
    pub subject: String,
    pub object: String,
    pub action: String,
    pub tenant_id: String,
}

/// 权限测试响应
#[derive(Debug, serde::Serialize)]
pub struct TestPermissionResponse {
    pub allowed: bool,
    pub subject: String,
    pub object: String,
    pub action: String,
    pub tenant_id: String,
    pub matched_policies: Vec<String>,
    pub test_timestamp: String,
}


/// 策略响应
#[derive(Debug, Serialize, Clone)]
pub struct PolicyResponse {
    pub id: Option<String>,
    pub policy_type: String, // "p" for permission, "g" for role
    pub subject: String,
    pub domain: String,
    pub object: Option<String>,
    pub action: Option<String>,
    pub effect: Option<String>,
    pub created_at: Option<String>,
}

/// 策略统计
#[derive(Debug, Serialize)]
pub struct PolicyStats {
    pub total_policies: i64,
    pub permission_policies: i64,
    pub role_policies: i64,
    pub tenant_id: Option<String>,
}

/// 策略创建请求
#[derive(Debug, Deserialize)]
pub struct CreatePolicyRequest {
    pub policy_type: String, // "permission" or "role"
    pub subject: String,
    pub domain: Option<String>,
    pub object: Option<String>,
    pub action: Option<String>,
    pub effect: Option<String>,
    pub role: Option<String>, // for role policies
}

/// 策略删除请求
#[derive(Debug, Deserialize)]
pub struct DeletePolicyRequest {
    pub tenant_id: String,
    pub policy_type: String,
    pub subject: String,
    pub object: Option<String>,
    pub action: Option<String>,
    pub effect: Option<String>,
    pub role: Option<String>,
}

/// 批量策略操作请求
#[derive(Debug, Deserialize)]
pub struct BatchPolicyRequest {
    pub tenant_id: String,
    pub policies: Vec<CreatePolicyRequest>,
}

/// 策略导出响应
#[derive(Debug, Serialize)]
pub struct PolicyExportResponse {
    pub tenant_id: Option<String>,
    pub policies: Vec<PolicyResponse>,
    pub total_count: usize,
    pub export_timestamp: String,
}

impl CasbinPolicyController {
    /// 获取策略列表 - 直接从数据库查询
    pub async fn get_policies(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<PolicyQueryParams>,
    ) -> Result<Json<PaginatedApiResponse<PolicyResponse>>, StatusCode> {
        let pool = &app_state.db;
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        // 构建查询条件
        let mut where_conditions = Vec::new();

        if let Some(subject) = &params.subject {
            where_conditions.push(format!("v0 ILIKE '%{}%'", subject.replace("'", "''").replace("%", "\\%").replace("_", "\\_")));
        }

        if let Some(object) = &params.object {
            where_conditions.push(format!("v2 ILIKE '%{}%'", object.replace("'", "''").replace("%", "\\%").replace("_", "\\_")));
        }

        if let Some(action) = &params.action {
            where_conditions.push(format!("v3 ILIKE '%{}%'", action.replace("'", "''").replace("%", "\\%").replace("_", "\\_")));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!(" AND {}", where_conditions.join(" AND "))
        };

        // 计算总数
        let count_query = format!("SELECT COUNT(*) FROM public.casbin_policies WHERE 1=1{}", where_clause);

        let total: i64 = match sqlx::query_scalar(&count_query).fetch_one(pool).await {
            Ok(count) => count,
            Err(e) => {
                tracing::error!("Failed to count policies: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        };

        // 分页参数
        let page = params.page.unwrap_or(1) as i32;
        let page_size = params.page_size.unwrap_or(20) as i32;
        let offset = (page - 1) * page_size;

        // 构建主查询
        let main_query = format!(
            "SELECT id, ptype, v0, v1, v2, v3, v4, created_at FROM public.casbin_policies WHERE 1=1{} ORDER BY created_at DESC LIMIT {} OFFSET {}",
            where_clause, page_size, offset
        );

        log::info!("Main query: {}", main_query);
        // 执行查询
        let rows = match sqlx::query(&main_query).fetch_all(pool).await {
            Ok(rows) => rows,
            Err(e) => {
                tracing::error!("Failed to fetch policies: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        };

        // 转换结果
        let mut policy_responses = Vec::new();
        for row in rows {
            let ptype: String = row.get("ptype");
            let v0: Option<String> = row.get("v0");
            let v1: Option<String> = row.get("v1");
            let v2: Option<String> = row.get("v2");
            let v3: Option<String> = row.get("v3");
            let v4: Option<String> = row.get("v4");
            let created_at: Option<chrono::DateTime<chrono::Utc>> = row.get("created_at");
            let id: i64 = row.get("id");

            let response = PolicyResponse {
                id: Some(id.to_string()),
                policy_type: match ptype.as_str() {
                    "p" => "permission".to_string(),
                    "g" => "role".to_string(),
                    "g2" => "role_inheritance".to_string(),
                    _ => ptype,
                },
                subject: v0.unwrap_or_default(),
                domain: v1.unwrap_or_default(),
                object: v2,
                action: v3,
                effect: v4,
                created_at: created_at.map(|dt| dt.to_rfc3339()),
            };
            policy_responses.push(response);
        }

        Ok(Json(PaginatedApiResponse::success(policy_responses, page, page_size, total, Some("获取策略列表成功".to_string()))))
    }

    /// 创建策略
    pub async fn create_policy(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<CreatePolicyRequest>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        let casbin_service = &app_state.casbin_service;
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        match request.policy_type.as_str() {
            "permission" => {
                let policy = PermissionPolicy {
                    subject: request.subject.clone(),
                    domain: request.domain.unwrap_or("*".to_string()),
                    object: request.object.unwrap_or_default(),
                    action: request.action.unwrap_or_default(),
                    effect: request.effect.unwrap_or("allow".to_string()),
                };
                
                tracing::debug!(
                    "Creating permission policy: subject='{}' ({}), domain='{}' ({}), object='{}' ({}), action='{}' ({}), effect='{}' ({})",
                    policy.subject, policy.subject.len(),
                    policy.domain, policy.domain.len(),
                    policy.object, policy.object.len(),
                    policy.action, policy.action.len(),
                    policy.effect, policy.effect.len()
                );

                match casbin_service.add_policy(&policy).await {
                    Ok(true) => Ok(Json(ApiResponse::success("策略创建成功".to_string(), Some("策略创建成功".to_string())))),
                    Ok(false) => Ok(Json(ApiResponse::success("策略已存在".to_string(), Some("策略已存在".to_string())))),
                    Err(e) => {
                        tracing::error!("Failed to create policy: {}", e);
                        Err(StatusCode::INTERNAL_SERVER_ERROR)
                    }
                }
            }
            "role" => {
                let role_relation = RoleRelation {
                    user: request.subject,
                    role: request.role.unwrap_or_default(),
                    domain: request.domain.unwrap_or("tenant_*".to_string()),
                };

                match casbin_service.add_role(&role_relation).await {
                    Ok(true) => Ok(Json(ApiResponse::success("角色关系创建成功".to_string(), Some("角色关系创建成功".to_string())))),
                    Ok(false) => Ok(Json(ApiResponse::success("角色关系已存在".to_string(), Some("角色关系已存在".to_string())))),
                    Err(e) => {
                        tracing::error!("Failed to create role relation: {}", e);
                        Err(StatusCode::INTERNAL_SERVER_ERROR)
                    }
                }
            }
            _ => Ok(Json(ApiResponse::success("无效的策略类型".to_string(), Some("无效的策略类型".to_string())))),
        }
    }

    /// 删除策略
    pub async fn delete_policy(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<DeletePolicyRequest>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        let casbin_service = &app_state.casbin_service;
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        match request.policy_type.as_str() {
            "permission" => {
                let policy = PermissionPolicy {
                    subject: request.subject,
                    domain: request.tenant_id,
                    object: request.object.unwrap_or_default(),
                    action: request.action.unwrap_or_default(),
                    effect: request.effect.unwrap_or("allow".to_string()),
                };

                match casbin_service.remove_policy(&policy).await {
                    Ok(true) => Ok(Json(ApiResponse::success("策略删除成功".to_string(), Some("策略删除成功".to_string())))),
                    Ok(false) => Ok(Json(ApiResponse::success("策略不存在".to_string(), Some("策略不存在".to_string())))),
                    Err(e) => {
                        tracing::error!("Failed to delete policy: {}", e);
                        Err(StatusCode::INTERNAL_SERVER_ERROR)
                    }
                }
            }
            "role" => {
                let role_relation = RoleRelation {
                    user: request.subject,
                    role: request.role.unwrap_or_default(),
                    domain: request.tenant_id,
                };

                match casbin_service.remove_role(&role_relation).await {
                    Ok(true) => Ok(Json(ApiResponse::success("角色关系删除成功".to_string(), Some("角色关系删除成功".to_string())))),
                    Ok(false) => Ok(Json(ApiResponse::success("角色关系不存在".to_string(), Some("角色关系不存在".to_string())))),
                    Err(e) => {
                        tracing::error!("Failed to delete role relation: {}", e);
                        Err(StatusCode::INTERNAL_SERVER_ERROR)
                    }
                }
            }
            _ => Ok(Json(ApiResponse::success("无效的策略类型".to_string(), Some("无效的策略类型".to_string())))),
        }
    }

    /// 获取策略统计 - 直接从数据库查询
    pub async fn get_policy_stats(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(_params): Query<PolicyQueryParams>,
    ) -> Result<Json<ApiResponse<PolicyStats>>, StatusCode> {
        let pool = &app_state.db;
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        // 查询统计数据
        let stats_query = "SELECT
                COUNT(*) as total_policies,
                COUNT(*) FILTER (WHERE ptype = 'p') as permission_policies,
                COUNT(*) FILTER (WHERE ptype = 'g') as role_policies,
                array_agg(DISTINCT v1) as tenants
            FROM public.casbin_policies".to_string();

        let row = match sqlx::query(&stats_query).fetch_one(pool).await {
            Ok(row) => row,
            Err(e) => {
                tracing::error!("Failed to fetch policy stats: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        };

        let total_policies: i64 = row.get("total_policies");
        let permission_policies: i64 = row.get("permission_policies");
        let role_policies: i64 = row.get("role_policies");
        let _tenants: Vec<String> = row.get::<Option<Vec<String>>, _>("tenants").unwrap_or_default().into_iter().filter(|t| !t.is_empty()).collect();

        let stats = PolicyStats {
            total_policies: total_policies,
            permission_policies: permission_policies,
            role_policies: role_policies,
            tenant_id: None,
        };

        Ok(Json(ApiResponse::success(stats, Some("获取策略统计成功".to_string()))))
    }

    /// 导出策略
    pub async fn export_policies(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<PolicyQueryParams>,
    ) -> Result<Json<ApiResponse<PolicyExportResponse>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let tenant_id = params.tenant_id.clone().unwrap_or_else(|| {
            // 如果没有指定租户ID，使用用户的第一个租户
            if let Some(first_role) = auth_context.roles.first() {
                first_role.tenant_id.map(|id| id.to_string()).unwrap_or_else(|| "default".to_string())
            } else {
                "default".to_string()
            }
        });

        // 获取策略列表（重用get_policies的逻辑）
        let policies_result = Self::get_policies(
            State(app_state),
            AuthExtractor(auth_context),
            Query(PolicyQueryParams {
                tenant_id: Some(tenant_id.clone()),
                subject: None,
                object: None,
                action: None,
                page: None,
                page_size: None,
            }),
        )
            .await?;

        let policies = policies_result.0.data;
        let total_count = policies.len();

        let export_response = PolicyExportResponse {
            tenant_id: Some(tenant_id),
            policies,
            total_count,
            export_timestamp: chrono::Utc::now().to_rfc3339(),
        };

        Ok(Json(ApiResponse::success(export_response, Some("导出策略成功".to_string()))))
    }

    /// 批量创建策略
    pub async fn batch_create_policies(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<BatchPolicyRequest>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let mut success_count = 0;
        let mut error_count = 0;

        for policy_request in request.policies {
            let create_request = CreatePolicyRequest {
                policy_type: policy_request.policy_type,
                subject: policy_request.subject,
                domain: policy_request.domain,
                object: policy_request.object,
                action: policy_request.action,
                effect: policy_request.effect,
                role: policy_request.role,
            };

            match Self::create_policy(State(app_state.clone()), AuthExtractor(auth_context.clone()), Json(create_request)).await {
                Ok(_) => success_count += 1,
                Err(_) => error_count += 1,
            }
        }

        Ok(Json(ApiResponse::success(
            format!("批量操作完成: 成功 {}, 失败 {}", success_count, error_count),
            Some("批量创建策略完成".to_string()),
        )))
    }

    /// 清除租户所有策略
    pub async fn clear_tenant_policies(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(tenant_id): Path<String>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        let casbin_service = &app_state.casbin_service;
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        match casbin_service.clear_tenant_policies(&tenant_id).await {
            Ok(_) => Ok(Json(ApiResponse::success("租户策略清除成功".to_string(), Some("租户策略清除成功".to_string())))),
            Err(e) => {
                tracing::error!("Failed to clear tenant policies: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 同步租户策略
    pub async fn sync_tenant_policies(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(tenant_id): Path<String>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        let casbin_service = &app_state.casbin_service;
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        match casbin_service.sync_permissions(&tenant_id).await {
            Ok(_) => Ok(Json(ApiResponse::success("租户策略同步成功".to_string(), Some("租户策略同步成功".to_string())))),
            Err(e) => {
                error!("Failed to sync tenant policies: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 根据数据范围获取角色列表
    pub async fn get_roles_by_data_scope(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(data_scope): Path<String>,
    ) -> Result<Json<ApiResponse<Vec<ScopeRole>>>, StatusCode> {

        let casbin_service = &app_state.casbin_service;
        // 权限检查
        if !auth_context.has_permission("policy", "read") {
            return Err(StatusCode::FORBIDDEN);
        }
        match casbin_service.get_roles_by_data_scope(data_scope).await {
            Ok(roles) => {
                Ok(Json(ApiResponse::success(
                    roles,
                    Some("角色列表获取成功".to_string()),
                )))
            },
            Err(e) => {
                error!("Failed to sync tenant policies: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 获取所有数据范围类型
    pub async fn get_all_data_scope_types(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
    ) -> Result<Json<ApiResponse<Vec<String>>>, StatusCode> {
        let pool = &app_state.db;
        // 权限检查
        if !auth_context.has_permission("policy", "read") {
            return Err(StatusCode::FORBIDDEN);
        }

        let casbin_policy_repo = CasbinPolicyRepository::new(pool.clone());

        match casbin_policy_repo.get_all_scope_types().await {
            Ok(scope_types) => {
                info!("Found {} data scope types", scope_types.len());
                Ok(Json(ApiResponse::success(
                    scope_types,
                    Some("数据范围类型列表获取成功".to_string()),
                )))
            },
            Err(e) => {
                error!("Failed to get all data scope types: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }



}

/// 创建 Casbin 策略管理路由 - 使用AppState进行状态管理
pub fn create_router() -> axum::Router<AppState> {
    use axum::routing::{get, post, delete};

    Router::new()
        .route("/policies", get(CasbinPolicyController::get_policies).post(CasbinPolicyController::create_policy))
        .route("/policies/batch", post(CasbinPolicyController::batch_create_policies))
        .route("/policies/delete", delete(CasbinPolicyController::delete_policy))
        .route("/policies/stats", get(CasbinPolicyController::get_policy_stats))
        .route("/policies/export", get(CasbinPolicyController::export_policies))
        .route("/tenant/{tenant_id}/clear", delete(CasbinPolicyController::clear_tenant_policies))
        .route("/tenant/{tenant_id}/sync", post(CasbinPolicyController::sync_tenant_policies))
        .route("/data-scope/{data_scope}/roles", get(CasbinPolicyController::get_roles_by_data_scope))
        .route("/data-scope/types", get(CasbinPolicyController::get_all_data_scope_types))
}
