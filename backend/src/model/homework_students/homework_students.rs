use bigdecimal::BigDecimal;
use chrono::{DateTime, Duration, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use qc_sqlx_derive::QcSqlxEnum;
use crate::model::PageParams;

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudents {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub class_id: Uuid,
    pub status: HomeworkStudentStatus,
    pub scores: Option<BigDecimal>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
#[derive(Clone, Debug, PartialEq, Eq, QcSqlxEnum, Serialize, Deserialize)]
#[serde(rename = "PascalCase")]
pub enum HomeworkStudentStatus {
    Unsubmitted,
    Error,
    Done,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct PageStudentsByHomeworkIdParams {
    pub homework_id: Uuid,
    pub page_params: PageParams,
    pub name_like: Option<String>,
    pub student_number: Option<String>,
    pub administrative_class_id: Option<Uuid>,
}
#[derive(Debug)]
///用于统计班级生的作业完成情况
pub struct StudentHomeworkSummary {
    pub student_id: Uuid,
    pub total_homework: i32,
    pub unsubmitted_count: i32,
}
//用于时间范围的枚举
#[derive(Debug, Clone, Copy, PartialEq, Deserialize, Serialize)]
pub enum TimeRange {
    #[serde(rename = "Week")]
    Week,
    #[serde(rename = "Month")]
    Month,
    #[serde(rename = "Year")]
    Year,
}
//转换数据
impl TimeRange {
    pub fn to_timestamp_range(&self) -> (i64, i64) {
        let now = Utc::now();
        let end = now.timestamp();
        let start = match self {
            TimeRange::Week => (now - Duration::days(7)).timestamp(),
            TimeRange::Month => (now - Duration::days(30)).timestamp(),
            TimeRange::Year => (now - Duration::days(365)).timestamp(),
        };
        (start, end)
    }
}