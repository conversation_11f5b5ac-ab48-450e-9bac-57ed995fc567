use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use qc_sqlx_derive::QcSqlxEnum;

#[derive(Debug, FromRow,Clone, Serialize)]
pub struct HomeworkFeedback {
    id: Uuid,
    homework_id: Uuid,
    student_id: Uuid,
    score_id: Option<Uuid>,
    text: String,
    status: HomeworkFeedbackStatus
}
#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq, QcSqlxEnum, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub enum HomeworkFeedbackStatus {
    Initial,
    Received,
    Rejected,
    Cancelled,
    Resubmitted,
}