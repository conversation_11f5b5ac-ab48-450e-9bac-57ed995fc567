import { PageParams } from ".";

export interface Homework {
  id: String;
  homework_name: String;
  homework_status: HomeworkStatusEnum;
  subject_group_id?: String;
  description?: String;
  leaf_count?: number,
  leaf_total?: number,
  page_count?: number,
  page_total?: number,
  created_at: String;
  updated_at: String;
}
export interface HomeworkLeafInfo {
  leaf_count?: number,
  leaf_total?: number,
  page_count?: number,
  page_total?: number,
}
export interface HomeworkSummary {
  paper_id: String | undefined;
  criteria: CriteriaSummary;
  scan: ScanSummary;
  homework_info: HomeworkLeafInfo;
  student: HomeworkStudentSummary;
  scoring: ScoringSummary;
}
 export interface CriteriaSummary {
  count: number;
  score: number;
 }

 export interface ScanSummary {
   unbounded_count: number;
   duplication_count: number;
   error_count: number;
   total_count: number;
 }
 export interface HomeworkStudentSummary {
   absent_count: number;
   error_count: number;
   total_count: number;
 }
 export interface ScoringSummary {
   total_count: number;
   scored_count: number;
   avg_score: number;
 }

export enum HomeworkStatusEnum {
  ///草稿，未发布的，学生看不到的状态
  Draft = "Draft",
  ///扫描处理过程中
  Doing = "Doing",
  ///作业已经完成，分数已经下发，后续状态不会再变，重评等都是实时更新
  Done = "Done",
}

export interface HomeworkStatistics {
  total_homeworks: number;
  completed_homeworks: number;
  in_progress_homeworks: number;
  total_students: number;
}

export interface CreateHomeworkParams {
  homework_name: String;
  homework_status: HomeworkStatusEnum;
  subject_group_id?: String;
  description?: String;
}

export interface UpdateHomeworkParams {
  id: String;
  homework_name: String;
  homework_status: HomeworkStatusEnum;
  subject_group_id?: String;
  description?: String;
}

export interface UpdateHomeworkLeafParams {
  id: String;
  homework_name: String;
  student_count:number;
  leaf_count: number;
  leaf_total:number;
  page_count:number;
  page_total:number;
}
export interface PageHomeworkParams {
  page_params: PageParams;
  name?: string;
  status?: HomeworkStatusEnum;
  subject_group_id?: string;
}