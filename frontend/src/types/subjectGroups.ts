export interface SubjectGroups {
  id: String,
  group_name: String,
  subject_code: String,
  description?: String,
  leader_user_id?: String,
  created_at?: String,
  updated_at?: String,
}

export interface CreateSubjectGroupsParams {
  group_name: String,
  subject_code: String,
  description?: String,
  leader_user_id?: String,
}

export interface UpdateSubjectGroupsParams {
  id: String,
  group_name?: String,
  subject_code?: String,
  description?: String | null,
  leader_user_id?: String | null,
}

export interface SubjectGroupsDetail {
  id: String,
  group_name: String,
  subject_code: String,
  description?: String,
  leader_user_id?: String,
  created_at?: String,
  updated_at?: String,
  //额外信息
  is_active?: Boolean,
  teacher_name?: String,
  subject_name?: String,
  member_count?: number,
  active_member_count?: number
}