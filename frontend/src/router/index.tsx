import RootLayoutWithPermissions from '@/layouts/RootLayout.tsx';
import IdentityBindingPage from '@/pages/Identity/IdentityBindingPage';
import IdentitySelectPage from '@/pages/Identity/IdentitySelectPage';
import LoginPage from '@/pages/LoginPage';
import { createBrowserRouter } from 'react-router-dom';
// Import page components
import AdministrativeClassesDetailPage from '@/pages/Class/AdministrativeClassesDetailPage';
import AdministrativeClassesPage from '@/pages/Class/AdministrativeClassesPage';
import TeachingClassesDetailPage from '@/pages/Class/TeachingClassDetailPage';
import TeachingClassesPage from '@/pages/Class/TeachingClassesPage';
import { EducationalStageManagementPage } from '@/pages/EducationalStageManagement';
import ExamManagementPage from '@/pages/ExamManagement';
import GradeManagementPage from '@/pages/GradeManagement';
import GradingCenterPage from '@/pages/GradingCenter';
import { RoleManagementPage } from '@/pages/RoleManagement';
import StatisticsPage from '@/pages/Statistics';
import { StudentManagementPage } from '@/pages/StudentManagement';
import { SubjectGroupsManagementPage } from '@/pages/SubjectGroupsManagement';
import SubjectManagement from '@/pages/SubjectManagement';
import { TeacherManagementPage } from '@/pages/TeacherManagement';
import TeachingAidsPage from '@/pages/TeachingAids';
import TeachingAidTextbookPreviewPage from '@/pages/TeachingAids/Preview';
import TenantManagementPage from '@/pages/Tenant/TenantManagementPage';
import UserManagementPage from '@/pages/UserManagement';
import HomeworkList from '@/pages/Student/HomeworkList';
import StudentReport from '@/pages/Student/StudentReport';

// Import enhanced protected route components
import AnswerSheetEditing from '@/pages/AnswerSheetEditing';
import CasbinPolicyManagementPage from '@/pages/CasbinPolicyManagement';
import { MenuManagementPage } from '@/pages/MenuManagement';
import ProfilePage from '@/pages/Profile';
import WorkflowPage from '@/pages/Workflow';
import WorkspacePage from '@/pages/Workspace';
import { homeworkSettingRoute } from './homework';
import { AdminProtectedRoute, BaseProtectedRoute, CombinedProtectedRoute, MenuProtectedRoute, PermissionProtectedRoute } from './ProtectedRoutes';
import { QuestionBasketContextProvider } from '@/contexts/QuestionBasketContext.tsx';
import Paper from '@/pages/Paper';
import ClassHomeworkAnalysis from '@/pages/Class/components/ClassHomeworkAnalysis';
import BookPreviewPage from '@/pages/TeachingAids/PreviewTest';

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/identitySelect',
    element: <IdentitySelectPage />,
  },
  {
    path: '/identityBinding',
    element: <IdentityBindingPage />,
  },
  {
    path: '/',
    element: <RootLayoutWithPermissions />,
    children: [
      {
        element: <BaseProtectedRoute />,
        children: [
          // 默认首页 - 工作台
          {
            index: true,
            element: (
              <BaseProtectedRoute>
                <WorkspacePage />
              </BaseProtectedRoute>
            ),
          },

          // 工作台
          {
            path: 'workspace',
            element: (
              <BaseProtectedRoute>
                <WorkspacePage />
              </BaseProtectedRoute>
            ),
          },

          // 教学辅助管理
          {
            path: 'teaching-aids',
            element: (
              <PermissionProtectedRoute>
                <TeachingAidsPage />
              </PermissionProtectedRoute>
            ),
          },
          {
            path: 'teaching-aids/textbook/:id',
            element: (
              <QuestionBasketContextProvider>
                <PermissionProtectedRoute>
                  <TeachingAidTextbookPreviewPage />
                </PermissionProtectedRoute>
              </QuestionBasketContextProvider>
            ),
          },
          {
            path: 'teaching-aids/book/:id',
            element: (
              <PermissionProtectedRoute>
                <BookPreviewPage />
              </PermissionProtectedRoute>
            ),
          },

          // 考试管理
          {
            path: 'exam-management',
            element: (
              <CombinedProtectedRoute menuId="exam_management">
                <ExamManagementPage />
              </CombinedProtectedRoute>
            ),
          },

          // 作业管理
          ...homeworkSettingRoute,
          {
            path: 'answerSheetEditing/:schema_name/:paperId',
            element: (
              <PermissionProtectedRoute>
                <AnswerSheetEditing />
              </PermissionProtectedRoute>
            ),
          },
          {
            path: 'paper',
            element: (
              <PermissionProtectedRoute>
                <Paper />
              </PermissionProtectedRoute>
            ),
          },
          {
            path: 'paper/:id',
            element: (
              <PermissionProtectedRoute>
                <Paper />
              </PermissionProtectedRoute>
            ),
          },
          // 班级管理
          {
            path: 'administrative-classes',
            element: (
              <MenuProtectedRoute menuId="administrative_classes_management">
                <AdministrativeClassesPage />
              </MenuProtectedRoute>
            ),
          },
          {
            path: 'administrative-classes/:classId',
            element: (
              <MenuProtectedRoute menuId="administrative_classes_management">
                <AdministrativeClassesDetailPage />
              </MenuProtectedRoute>
            ),
          },
          {
            path: 'teaching-classes',
            element: (
              <PermissionProtectedRoute>
                <TeachingClassesPage />
              </PermissionProtectedRoute>
            ),
          },
          {
            path: 'teaching-classes/:classId',
            element: (
              <PermissionProtectedRoute>
                <TeachingClassesDetailPage />
              </PermissionProtectedRoute>
            ),
          },
          //班级汇总
          {
            path: 'classes-summary/:classId',
            element: (
              <MenuProtectedRoute menuId="administrative_classes_management">
                <ClassHomeworkAnalysis />
              </MenuProtectedRoute>
            ),
          },

          // 阅卷中心
          {
            path: 'grading-center',
            element: (
              <MenuProtectedRoute menuId="grading_center">
                <GradingCenterPage />
              </MenuProtectedRoute>
            ),
          },

          // 统计分析
          {
            path: 'statistics',
            element: (
              <PermissionProtectedRoute>
                <StatisticsPage />
              </PermissionProtectedRoute>
            ),
          },

          // 学生管理
          {
            path: 'students',
            element: (
              <CombinedProtectedRoute menuId="student_management">
                <StudentManagementPage />
              </CombinedProtectedRoute>
            ),
          },

          // 教师管理
          {
            path: 'teachers',
            element: (
              <CombinedProtectedRoute menuId="teacher_management">
                <TeacherManagementPage />
              </CombinedProtectedRoute>
            ),
          },

          // 年级管理
          {
            path: 'grades',
            element: (
              <PermissionProtectedRoute>
                <GradeManagementPage />
              </PermissionProtectedRoute>
            ),
          },

          // 学科管理
          {
            path: 'subjects',
            element: (
              <PermissionProtectedRoute>
                <SubjectManagement />
              </PermissionProtectedRoute>
            ),
          },

          // 学科组管理
          {
            path: 'subject-groups',
            element: (
              <PermissionProtectedRoute>
                <SubjectGroupsManagementPage />
              </PermissionProtectedRoute>
            ),
          },

          // 学段管理
          {
            path: 'education-stages',
            element: (
              <PermissionProtectedRoute>
                <EducationalStageManagementPage />
              </PermissionProtectedRoute>
            ),
          },

          // 系统管理 - 需要管理员权限
          {
            path: 'tenants',
            element: (
              <AdminProtectedRoute>
                <TenantManagementPage />
              </AdminProtectedRoute>
            ),
          },
          {
            path: 'casbin-policies',
            element: (
              <AdminProtectedRoute>
                <CasbinPolicyManagementPage />
              </AdminProtectedRoute>
            ),
          },
          {
            path: 'menu-management',
            element: (
              <AdminProtectedRoute>
                <MenuManagementPage />
              </AdminProtectedRoute>
            ),
          },
          {
            path: 'users',
            element: (
              <CombinedProtectedRoute requireAll={false}>
                <UserManagementPage />
              </CombinedProtectedRoute>
            ),
          },
          {
            path: 'roles',
            element: (
              <CombinedProtectedRoute menuId="role_management">
                <RoleManagementPage />
              </CombinedProtectedRoute>
            ),
          },
          {
            path: 'profile',
            element: (
              <BaseProtectedRoute>
                <ProfilePage />
              </BaseProtectedRoute>
            ),
          },
          { path: 'workflow', element: <WorkflowPage /> },
          {
            path: 'workflow',
            element: (
              <CombinedProtectedRoute>
                <WorkflowPage />
              </CombinedProtectedRoute>
            ),
          },
          {
            path: 'student-score',
            element: (
              <PermissionProtectedRoute>
                <HomeworkList />
              </PermissionProtectedRoute>
            ),
          },
          {
            path: 'student-score/report',
            element: (
              <PermissionProtectedRoute>
                <StudentReport />
              </PermissionProtectedRoute>
            ),
          },
        ],
      },
    ],
  },
]);
