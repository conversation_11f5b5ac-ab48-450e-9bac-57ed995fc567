import React, { CSSProperties, useCallback, useContext, useMemo } from 'react';
import { useStore } from 'zustand';
import { ComponentDataListStoreContext, ToolbarDataStoreContext } from '..';
import { pageIndexItemIdEx } from '../store/componentDataListStore';
import { AnswerBlockData, PaperDataStoreContext } from '../store/paperDataStore';
import '../ui/global.scss';
import { QuestionCardUI } from '../ui/question-card-components/global';
import BlockArea from './base-component/page/BlockArea';
import PageBackground from './base-component/page/page-background';
export interface Props {
  /**
   * 页码
   */
  pageIndex: number;
  /**
   * 总页数
   */
  pageTotals: number;
  /**
   * 要渲染的每个桶的组件列表
   */
  bucketNodeIdList: string[][];
  /**
   * 页面样式
   */
  pageStyle?: CSSProperties;
  /**
   * 返回桶高度的回调
   */
  getBucketHeight?: (height: number) => void;
}
/**
 * 作者：张瀚
 * 说明：页面组件
 */
export function CanvasPageLayer({ pageIndex, pageTotals, bucketNodeIdList, pageStyle, getBucketHeight }: Props) {
  //取出工具栏数据
  const usePaperDataStoreContext = useContext(PaperDataStoreContext);
  const answerCard = useStore(usePaperDataStoreContext, (state) => state.answerCard);
  const bucketNodeIdToBucketNodeMap = useStore(usePaperDataStoreContext, (state) => state.bucketNodeIdToBucketNodeMap);
  const bucketNodeIdToBlockIdListMap = useStore(usePaperDataStoreContext, (state) => state.bucketNodeIdToBlockIdListMap);
  const questionItemIdToBucketNodeIdListMap = useStore(usePaperDataStoreContext, (state) => state.questionItemIdToBucketNodeIdListMap);
  const blockIdToBlockMap = useStore(usePaperDataStoreContext, (state) => state.blockIdToBlockMap);
  const useToolbarDataStoreContext = useContext(ToolbarDataStoreContext);
  const showAnswerBox = useStore(useToolbarDataStoreContext, (state) => state.showAnswerBox);
  const answerBoxVersion = useStore(useToolbarDataStoreContext, (state) => state.answerBoxVersion);
  const pageBackground = useStore(useToolbarDataStoreContext, (state) => state.pageBackground);
  const showPageBackground = useStore(useToolbarDataStoreContext, (state) => state.showPageBackground);
  const userComponentDataListStoreContext = useContext(ComponentDataListStoreContext);
  const bucketNodeIdToRealDomMap = useStore(userComponentDataListStoreContext, (state) => state.bucketNodeIdToRealDomMap);
  const nowEditingQuestionItemId = useStore(userComponentDataListStoreContext, (state) => state.nowEditingQuestionItemId);

  const AnswerAreaBoxList = useCallback(
    (nodeIdList: string[]) => {
      return nodeIdList.map((nodeId) => {
        const blockIdList = bucketNodeIdToBlockIdListMap.get(nodeId);
        if (!blockIdList) {
          return;
        }
        const blockList = blockIdList
          .map((blockId) => {
            return blockIdToBlockMap.get(blockId);
          })
          .filter((it) => it !== undefined);
        if (!blockList || blockList.length == 0) {
          return;
        }
        return (
          <React.Fragment key={`${nodeId}-fragment`}>
            {blockList.map((block, i) => {
              if (!block) {
                return;
              }
              const areaId = `${nodeId}_${i}`;
              return <BlockArea key={areaId} id={areaId} block={block} />;
            })}
          </React.Fragment>
        );
      });
    },
    [blockIdToBlockMap, bucketNodeIdToBlockIdListMap],
  );

  //当前选中的题块显示出来，全显示的时候就没必要了
  const NowEditingQuestionAnswerAreaBox = useCallback(() => {
    if (!nowEditingQuestionItemId || showAnswerBox) {
      return;
    }
    const nowBucketNodeIdList = questionItemIdToBucketNodeIdListMap.get(nowEditingQuestionItemId);
    if (!nowBucketNodeIdList || nowBucketNodeIdList.length === 0) {
      return;
    }
    //不在此页面内的不显示
    const nowPageNodeIdList = bucketNodeIdList.flat();
    const newNodeIdList = nowBucketNodeIdList.filter((nodeId) => nowPageNodeIdList.includes(nodeId));
    if (newNodeIdList.length === 0) {
      return;
    }
    return <React.Fragment key={answerBoxVersion}>{AnswerAreaBoxList(newNodeIdList)}</React.Fragment>;
  }, [AnswerAreaBoxList, answerBoxVersion, bucketNodeIdList, nowEditingQuestionItemId, questionItemIdToBucketNodeIdListMap, showAnswerBox]);

  return (
    <div
      className="canvas-page-layer font-default"
      style={{
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        width: `${Math.floor(answerCard.page_orientation_is_vertical ? answerCard.width : answerCard.height)}px`,
        height: `${Math.floor(answerCard.page_orientation_is_vertical ? answerCard.height : answerCard.width)}px`,
        padding: `${answerCard.y}px ${answerCard.right}px ${answerCard.bottom}px ${answerCard.x}px `,
        ...pageStyle,
      }}
    >
      {answerCard.show_pos_point && PosPointComponent(answerCard.pos_point_width, answerCard.pos_point_height)}
      <QuestionCardUI.Flex
        gap={10}
        style={{ flexGrow: 1, height: 0 }}
        vertical={false}
        ref={(el) => {
          el && getBucketHeight && getBucketHeight(el.offsetHeight);
        }}
      >
        {Array(answerCard.bucket_size)
          .fill(undefined)
          .map((_, bucketIndex) => {
            return (
              <div key={`page_${bucketIndex}`} style={{ flexGrow: 1, width: 0 }}>
                {bucketNodeIdList[bucketIndex]?.map((d) => bucketNodeIdToBucketNodeMap.get(d)?.node)}
              </div>
            );
          })}
      </QuestionCardUI.Flex>
      {answerCard.show_page_index && PageIndexComponent(pageIndex, pageTotals, bucketNodeIdToRealDomMap)}
      {answerCard.show_pos_point && PosPointComponent(answerCard.pos_point_height, answerCard.pos_point_width)}
      {showAnswerBox && AnswerAreaBox(pageIndex, answerCard.show_page_index, bucketNodeIdToBlockIdListMap, blockIdToBlockMap, bucketNodeIdList, answerBoxVersion)}
      {
        //当前选中的题块显示出来，全显示的时候就没必要了
        <NowEditingQuestionAnswerAreaBox />
      }
      {pageBackground && showPageBackground && <PageBackground key={pageBackground} image_src={pageBackground}></PageBackground>}
    </div>
  );
}

function AnswerAreaBox(
  pageIndex: number,
  showPageIndex: boolean,
  bucketNodeIdToBlockIdListMap: Map<string, string[]>,
  blockIdToBlockMap: Map<string, AnswerBlockData>,
  bucketNodeIdList: string[][],
  answerBoxVersion: string,
) {
  //获取组件信息
  const pageIndexBlockIdList = bucketNodeIdToBlockIdListMap.get(`${pageIndexItemIdEx}_${pageIndex}_0`) ?? [];
  return (
    <React.Fragment key={answerBoxVersion}>
      {bucketNodeIdList.map((nodeIdList) => {
        return nodeIdList.map((nodeId) => {
          const blockIdList = bucketNodeIdToBlockIdListMap.get(nodeId);
          if (!blockIdList) {
            return;
          }
          const blockList = blockIdList
            .map((blockId) => {
              return blockIdToBlockMap.get(blockId);
            })
            .filter((it) => it !== undefined);
          if (!blockList || blockList.length == 0) {
            return;
          }
          return (
            <React.Fragment key={`${nodeId}-fragment`}>
              {blockList.map((block, i) => {
                if (!block) {
                  return;
                }
                const areaId = `${nodeId}_${i}`;
                return <BlockArea key={areaId} id={areaId} block={block} />;
              })}
            </React.Fragment>
          );
        });
      })}
      {showPageIndex &&
        pageIndexBlockIdList.map((blockId) => {
          const block = blockIdToBlockMap.get(blockId);
          if (!block) {
            return;
          }
          const { serial_number } = block;
          const areaId = `${pageIndexItemIdEx}-${pageIndex}_${serial_number}`;
          return <BlockArea key={areaId} id={areaId} block={block} />;
        })}
    </React.Fragment>
  );
}

/**
 * 作者：张瀚
 * 说明：顶部定位点
 */
function PosPointComponent(width: number, height: number) {
  return (
    <QuestionCardUI.Flex justify="space-between" align="center">
      <div style={{ height: `${height}px`, width: `${width}px`, background: 'black' }}></div>
      <div style={{ height: `${height}px`, width: `${width}px`, background: 'black' }}></div>
    </QuestionCardUI.Flex>
  );
}

/**
 * 作者：张瀚
 * 说明：页码组件
 */
function PageIndexComponent(pageIndex: number, pageTotals: number, bucketNodeIdToRealDomMap: Map<string, HTMLElement>) {
  //页码数据
  const pageIndexBlockArray = useMemo(() => {
    let arr = pageIndex ? pageIndex.toString(2).split('') : [];
    while (arr.length < 3) {
      arr.unshift('0');
    }
    const allPageIn2 = pageTotals ? pageTotals.toString(2).split('') : [];
    while (arr.length < allPageIn2.length) {
      arr.unshift('0');
    }
    return arr;
  }, [pageIndex, pageTotals]);
  return (
    <QuestionCardUI.Flex
      key={`${pageIndex}_${pageTotals}`}
      justify="center"
      style={{ lineHeight: '30px', fontSize: '14px' }}
      align="center"
      ref={(el) => {
        //第一页的才作为页码的node
        if (!el || pageIndex !== 1) {
          return;
        }
        bucketNodeIdToRealDomMap.set(`${pageIndexItemIdEx}_${pageIndex}_0`, el);
      }}
    >
      {pageIndexBlockArray.map((b, i) => (
        <div key={i} className={`page-index-block`} style={{ height: '20px', width: '20px', border: '1px solid black', marginRight: '20px', background: b === '1' ? 'black' : 'white' }} />
      ))}
      {/* <div style={{ marginLeft: '50px' }}>第 {pageIndex} 页，共 {pageIndexSize} 页</div> */}
    </QuestionCardUI.Flex>
  );
}
