import { MathJ<PERSON> } from 'better-react-mathjax'
import { useMemo } from 'react'
import './Mathdown.scss'
import { latexDollarFix, latexTableFix, splitText } from './baseTools'

const Mathdown = ({ content = '' }) => {
  const mathContent = useMemo(() => {
    const totalTextList = splitText(content)
    const tableRegex = /<table[^>]*?>[\s\S]*?<\/table>/
    let newContent = ''
    //表格类型修正
    for (const paragraphText of totalTextList) {
      let content = '' + paragraphText
      const tableMatch = content.match(tableRegex)
      if (tableMatch) {
        newContent += latexTableFix(content)
      } else {
        newContent += latexDollarFix(content)
      }
    }
    return newContent
  }, [content])

  return <MathJax className='mathdown' dynamic={true} dangerouslySetInnerHTML={{ __html: mathContent }}></MathJax>
}

export default Mathdown
