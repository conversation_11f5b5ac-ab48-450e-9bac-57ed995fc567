/**
 * 作者：张瀚
 * 标准：
 * 1、行高统一为32px（2倍16px行高），如果有border（1px）则改为30px以确保每一行的组件和文字等高，非文字类组件也可以通过padding一类控制到32px
 */
$background-color: #fafafa;
$border-color: #d9d9d9;
$border-radius: 6px;
$border-radius-large: 8px;
$font-size: 16px;

.unset {
  padding: 0;
  margin: 0;
  outline: none;
  border: none;
  outline: none;
  cursor: default;
  background: none;
  box-sizing: initial;
}

.font-default {
  // font-family: -apple-system,
  //   // iOS 和 macOS (San Francisco)
  //   BlinkMacSystemFont,
  //   //macOS Chrome
  //   "Segoe UI",
  //   // Windows
  //   Roboto,
  //   // Android 和 Material Design
  //   Oxygen-Sans,
  //   // KDE 系统
  //   Ubuntu,
  //   // Ubuntu
  //   Cantarell,
  //   // GNOME 
  //   "Helvetica Neue",
  //   // 旧版 macOS 
  //   Arial,
  //   // 通用后备 
  //   "Noto Sans",
  //   // 多语言支持 
  //   sans-serif,
  //   // 最终后备 
  //   system-ui,
  //   //现代系统字体
  //   "PingFang SC",
  //   // 苹果简体中文
  //   "Microsoft YaHei",
  //   // 微软雅黑
  //   "WenQuanYi Micro Hei",
  //   // 开源文泉驿
  //   sans-serif;
  font-family: 'Segoe UI',Arial,'Microsoft Yahei',sans-serif;
  line-height: 32px;
  font-size: $font-size;
}

.border-default {
  @extend .disabled;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  line-height: 30px;

  &:focus-visible {
    outline: none;
  }
}

.hover-default {
  background-color: $background-color;

  &:hover {
    border-color: #171717;
    color: #171717;
    background-color: #ebeef5;
  }

  &:active {
    border-color: #000000;
    color: #000000;
    background-color: #e4e7ed;
  }
}

.disabled {
  &:disabled {

    &:hover,
    & {
      background-color: #e4e7ed !important;
      color: #171717 !important;
      border: 1px solid $border-color !important;
      cursor: not-allowed !important;
    }
  }
}

.flex-center {
  display: flex;
  align-content: center;
  align-items: center;
}

.flex-center-gap10 {
  @extend .flex-center;
  gap: 10px;
}

.flex-center-gap10-column {
  @extend .flex-center-gap10;
  flex-direction: column;
}

.flex-grow {
  flex-grow: 1;
}

//左滑进出动画
.translate-x-show {
  transition:
    transform 0.5s ease-in-out,
    opacity 0.5s ease-in-out;
  /* 初始状态 */
  transform: translateX(-100vw);
  opacity: 0;

  &.showing {
    transform: translateX(0);
    /* 向左滑出视窗 */
    opacity: 1;
  }
}

//渐隐消失动画
.opacity-show {
  transition: opacity 0.5s ease-in-out;
  /* 初始状态 */
  opacity: 0;

  &.showing {
    opacity: 1;
  }
}

//绿色高亮色边框
.high-light-border-green {
  border: 1px solid rgb(57, 255, 20);
  box-shadow: 0 0 8px rgba(57, 255, 20, 0.7);
}

//红色高亮色边框
.high-light-border-red {
  border: 1px solid rgb(255, 0, 0);
  box-shadow: 0 0 8px rgba(255, 0, 0, 0.7);
}