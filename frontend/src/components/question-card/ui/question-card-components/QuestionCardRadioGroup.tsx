import { RadioGroup } from 'radix-ui'
import '../global.scss'
import './QuestionCardRadioGroup.scss'
import React from "react";
export interface Props extends RadioGroup.RadioGroupProps {
  options?: {
    value: string
    label: React.JSX.Element | string
  }[]
}
export function QuestionCardRadioGroup({ options, ...props }: Props) {
  return (
    <RadioGroup.Root {...props} className='radio-group-root flex-center-gap10'>
      {options?.map((op) => {
        return (
          <div className='flex-center-gap10' key={op.value}>
            <RadioGroup.Item className='radio-group-item' value={op.value} id={op.value}>
              <RadioGroup.Indicator className='radio-group-indicator' />
            </RadioGroup.Item>
            {op.label}
          </div>
        )
      })}
    </RadioGroup.Root>
  )
}
