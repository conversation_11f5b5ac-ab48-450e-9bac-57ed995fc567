import { ApiResponse, PaginatedApiResponse } from '@/types';
import apiClient from './apiClient';
import { type Scores } from './homeworkReviewApi';

// 批次图片类型
export interface BatchImage {
  id: string;
  url: string;
  page_number: number;
  student_id?: string;
  student_name?: string;
  is_error?: boolean;
  status?: 'normal' | 'ocr_error' | 'blank' | 'duplicate' | 'pending';
  created_at: string;
  updated_at: string;
}

// 批次类型
export interface BatchList {
  exam_id: string;
  batch_no: string;
  created_at: Date;
  status: 'Undistributed' | 'Error' | 'Done';
  completed_count: number;
  error_scan_count: number;
  total_duplicate_count: number;
  processing_count: number;
  total_scan_count: number;
  total_unbound_count: number;
}

// 批次试卷扫描文件列表
export interface BatchImageList {
  id: string;
  pages: Page[];
  status: 'Undistributed' | 'Unbound' | 'Duplicate' | 'Error' | 'Done'; // Undistributed:未发布,Unbound:未绑定学生信息,Duplicate:重复页,Error:异常错误,Done:已完成
  student_id: string;
  student_number: string;
  batch_number: string;
  created_at: Date;
  student: Student;
}
export interface Page {
  id: string;
  file_name: string;
  page_num: number;
  file_size: number;
  url: string;
  rectify_url: null | string;
  is_duplicate: boolean;
  is_blank: boolean;
  is_abnormal: boolean;
  abnormal_reason: null | string;
}

export interface Student {
  student_id: string | null;
  student_name: string | null;
  class_name?: string | null;
  student_number: string | null;

}

// 扫描统计类型
export interface ScanStats {
  total_scanned: number;
  pending_processing: number;
  processing_complete: number;
  exception_count: number;
}

// 学生搜索结果类型
export interface StudentSearchResult {
  id: string;
  student_id: string;
  name: string;
  class: string;
  grade: string;
}

// 扫描批次图片详情
export interface BatchDetail {
  id: string;
  exam_type: string;
  batch_no: string;
  student_id: string;
  student_number: string;
  created_at: Date;
  pages: PageImage[];
  score_or_blocks: ScoreOrBlock[];
  student: Student | null;
}

export interface PageImage {
  file_url: string;
  rectify_url: string;
  is_blank: boolean;
  is_abnormal: boolean;
  abnormal_reason: string | undefined;
  page_id: string;
  page_num: number;
}

export interface ScoreOrBlock {
  Block: any[];
  Score: ScoreDetail;
}

export interface ScoreDetail {
  criteria: Criteria;
  score: Scores;
}

export interface Criteria {
  id: string;
  answer: string;
  check_work_id: string | null;
  criteriaName: string | null;
  mode: string;
  ocr_work_id: string | null;
  score: number;
  scoring_type: 'Match' | 'Ai' | 'Tracer' | 'Man' | 'Audit';
}

export interface Statistics {
  total_scan_count: number;
  processing_count: number;
  total_unbound_count: number;
  total_duplicate_count: number;
  error_scan_count: number;
  completed_count: number;
}

export interface ReScoringParams {
  criteria_ids: string[]; // 需要重新评分的评分标准ID集合
  student_ids: string[]; // 需要重新评分的学生ID列表
  homework_id: string; // 关联的作业ID
  redo_ocr: boolean; // 是否重新进行OCR识别
  include_done: boolean; // 是否包含已完成的作业
  include_checked: boolean; // 是否包含已检查的作业
}

export interface rebindStudentParams {
  exam_id: string;
  student_id?: string;
  target_student_id: string;
  page_ids: string[];
}

export interface RescanParams {
  exam_id: string;
  batch_number?: string;
  pages_ids: string[];
}

export interface PageBlankParams {
  exam_id: string;
  page_id: string;
}


// 扫描API
export const scanApi = {
  /**
   * 获取批次列表
   */
  getBatchNumberList: async (tenantName: string, exam_id: string): Promise<ApiResponse<BatchList>> => {
    return apiClient.get(`/api/v1/grading/${tenantName}/scans/getBatchNumberList?exam_id=${exam_id}`);
  },

  /**
   * 获取批次详情
   */
  getBatchDetail: async (params: { tenantName: string; exam_id: string; batch_number?: string; page: number; page_size: number; status?: string[] }): Promise<PaginatedApiResponse<BatchImageList>> => {
    return apiClient.post(`/api/v1/grading/${params.tenantName}/scans/getPaperScanFiles`, params);
  },

  /**
   * 获取批次图片详情
   */
  getBatchImageDetails: async (
    tenantName: string,
    params: {
      sheet_id?: string;
      student_id?: string;
      homework_id: string;
    },
  ): Promise<ApiResponse<BatchDetail>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/sheets/detail`, {
      sheet_id: params.sheet_id,
      student_id: params.student_id,
      homework_id: params.homework_id,
    });
  },

  /**
   * 查询试卷扫描总览信息
   */
  getStatistics: async (tenantName: String, exam_id: String): Promise<ApiResponse<Statistics>> => {
    return apiClient.get(`/api/v1/grading/${tenantName}/scans/getStatistics?exam_id=${exam_id}`);
  },

  /**
   * 批量清除扫描批次纸张信息
   */
  clearBatchPaperInfo: async (
    tenantName: string,
    params: {
      exam_id: string;
      batch_number?: string;
    },
  ): Promise<ApiResponse<BatchDetail>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/scans/batchDeletePaperScans`, params);
  },

  /**
   * 根据纸张页面page_id,删除指定页面信息
   */
  deletePaperScanPage: async (
    tenantName: string,
    params: {
      exam_id: String;
      page_ids: string[];
    },
  ): Promise<ApiResponse<BatchDetail>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/scans/deletePaperScanPage`, params);
  },

  /**
   * 重阅
   */
  redoScore: async (tenantName: string, params: ReScoringParams): Promise<ApiResponse<number>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/redo_scoring`, params);
  },

  /**
   * 重绑学生与纸张对应关系
   */
  rebindStudentToLeaf: async (
    tenantName: string,
    params: rebindStudentParams,
  ): Promise<ApiResponse<{}>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/scans/rebindStudentToLeaf`, params);
  },

  /**
   * 重新识别纸张内容 (会触发从新识别服务调用)
   */
  rescanPage: async (
    tenantName: string,
    params: RescanParams,
  ): Promise<ApiResponse<{}>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/batchRecognizeScannedPagesHandler`, params);
  },
  /**
   * 重新识别纸张内容 (会触发从新识别服务调用)
   */
  setPageBlank: async (
      tenantName: string,
      params: PageBlankParams,
  ): Promise<ApiResponse<{}>> => {
    return apiClient.post(`/api/v1/grading/${tenantName}/scans/setPageBlank`, params);
  },
};

export default scanApi;
