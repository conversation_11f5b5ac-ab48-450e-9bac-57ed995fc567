import { createApiHeaders } from "@/lib/apiUtils";
import { ApiResponse, PaginatedApiResponse } from "@/types";
import {
  CreateHomeworkParams,
  Homework,
  HomeworkStatistics, HomeworkSummary,
  PageHomeworkParams, UpdateHomeworkLeafParams,
  UpdateHomeworkParams,
} from "@/types/homework";
import apiClient from "./apiClient";

/**
 * 作者：张瀚
 * 说明：作业API
 */
export const homeworkApi = {
  /**
   * 作者：张瀚
   * 说明：获取作业统计数据
   */
  getStatistics: async (
    tenant_id: string,
    tenant_name: string
  ): Promise<ApiResponse<HomeworkStatistics>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/getStatistics`,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：张瀚
   * 说明：创建作业
   */
  createHomework: async (
    tenant_id: string,
    tenant_name: string,
    params: CreateHomeworkParams
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/createHomework`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：张瀚
   * 说明：更新作业
   */
  updateHomework: async (
    tenant_id: string,
    tenant_name: string,
    params: UpdateHomeworkParams
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/updateHomework`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  updateHomeworkLeafInfo:async(tenant_id: string, tenant_name: string, params: UpdateHomeworkLeafParams):Promise<ApiResponse<Homework>> =>{
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/updateHomeworkLeafInfo`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：朱若彪
   * 说明：分页查询作业
   */
  pageHomework: async (
    tenant_id: string,
    tenant_name: string,
    params: PageHomeworkParams
  ): Promise<PaginatedApiResponse<Homework>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/pageHomework`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：朱若彪
   * 说明：根据id查询作业信息
   */
  getHomeworkById: async (
    tenant_id: string,
    tenant_name: string,
    id: string
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.get(
      `/api/v1/tenants/${tenant_name}/homework/getHomeworkById/${id}`,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  getHomeworkSummary: async (
      tenant_id: string,
      tenant_name: string,
      id: string
  ): Promise<ApiResponse<HomeworkSummary>> => {
    return apiClient.post(
        `/api/v1/tenants/${tenant_name}/homework/${id}/summary`,
        {
          headers: createApiHeaders(tenant_id),
        }
    );
  },
  /**
    * 作者：朱若彪
    * 说明：删除作业
  */
  deleteHomework: async (
    tenant_id: string,
    tenant_name: string,
    id: string
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.delete(`/api/v1/tenants/${tenant_name}/homework/${id}/delete`,
      {
        headers: createApiHeaders(tenant_id),
      });
  }
};

// 工作台专用类型定义
export interface WorkspaceHomeworkTask {
  id: string;
  title: string;
  type: 'homework';
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  priority: 'high' | 'medium' | 'low';
  assignee: string;
  createdAt: string;
  dueDate?: string;
  subject?: string;
  grade?: string;
  description?: string;
}

// 数据转换函数
// 根据作业状态映射任务状态
const transformToWorkspaceHomeworkTask = (homework: Homework): WorkspaceHomeworkTask => {
  const getTaskStatus = (homeworkStatus: string): 'pending' | 'in_progress' | 'completed' | 'error' => {
    switch (homeworkStatus.toLowerCase()) {
      case 'draft':
        return 'pending';
      case 'doing':
        return 'in_progress';
      case 'done':
        return 'completed';
      default:
        return 'pending';
    }
  };

  return {
    id: String(homework.id),
    title: String(homework.homework_name),
    type: 'homework',
    status: getTaskStatus(String(homework.homework_status)),
    priority: 'medium', // 默认优先级
    assignee: '未知', // 暂无创建者信息
    createdAt: homework.created_at ? new Date(String(homework.created_at)).toISOString().split('T')[0] : '',
    description: homework.description ? String(homework.description) : undefined,
    subject: undefined, // 暂无学科信息
    grade: undefined, // 暂无年级信息
  };
};

// 工作台专用API适配层
export const WorkspaceHomeworkApi = {
  /**
   * 获取最近作业任务（工作台专用）
   */
  getRecentHomeworkTasks: async (tenant_id: string, tenant_name: string, limit: number = 6): Promise<WorkspaceHomeworkTask[]> => {
    const params: PageHomeworkParams = {
      page_params: {
        page: 1,
        page_size: limit,
      },
    };

    const response = await homeworkApi.pageHomework(tenant_id, tenant_name, params);
    
    if (!response.data) {
      throw new Error('Failed to fetch homework tasks');
    }

    return response.data.map(transformToWorkspaceHomeworkTask);
  },
};
