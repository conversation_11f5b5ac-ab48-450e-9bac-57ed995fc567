import apiClient from './apiClient';
import { SimpleRole } from '../types/role';
import { ApiResponse, PaginatedApiResponse } from '../types';

// 策略相关类型定义
export interface CasbinPolicy {
  id?: string;
  policy_type: string;  // "permission" or "role"
  subject: string;
  domain: string;
  object?: string;
  action?: string;
  effect?: string;
  created_at?: string;
}

export interface PolicyStats {
  total_policies: number;
  permission_policies: number;
  role_policies: number;
  tenants: string[];
}

export interface PolicyTestRequest {
  tenant_id: string;
  subject: string;
  object: string;
  action: string;
}

export interface PolicyTestResponse {
  allowed: boolean;
  subject: string;
  object: string;
  action: string;
  tenant_id: string;
  matched_policies: CasbinPolicy[];
  test_timestamp: string;
}

export interface CreatePolicyRequest {
  tenant_id: string;
  policy_type: string;
  subject: string;
  object?: string;
  action?: string;
  effect?: string;
  role?: string;
}

export interface DeletePolicyRequest {
  tenant_id: string;
  policy_type: string;
  subject: string;
  object?: string;
  action?: string;
  effect?: string;
  role?: string;
}

export interface BatchPolicyRequest {
  tenant_id: string;
  policies: CreatePolicyRequest[];
}

export interface PolicyExportResponse {
  tenant_id?: string;
  policies: CasbinPolicy[];
  total_count: number;
  export_timestamp: string;
}

// 移除本地ApiResponse接口定义，使用全局导入的版本

// Policy API 服务
export const policyApi = {
  /**
   * 获取策略列表
   */
  async getPolicies(params?: {
    tenant_id?: string;
    subject?: string;
    object?: string;
    action?: string;
    page?: number;
    page_size?: number;
  }): Promise<PaginatedApiResponse<CasbinPolicy>> {
    return await apiClient.get('/api/v1/casbin-policies/policies', { params });
  },

  /**
   * 创建策略
   */
  async createPolicy(policy: CreatePolicyRequest): Promise<ApiResponse<string>> {
    const response = await apiClient.post('/api/v1/casbin-policies/policies', policy);
    return response.data;
  },

  /**
   * 删除策略
   */
  async deletePolicy(policy: DeletePolicyRequest): Promise<ApiResponse<string>> {
    const response = await apiClient.delete('/api/v1/casbin-policies/policies', { data: policy });
    return response.data;
  },

  /**
   * 测试权限
   */
  async testPermission(request: PolicyTestRequest): Promise<ApiResponse<PolicyTestResponse>> {
    const response = await apiClient.post('/api/v1/casbin-policies/policies/test', request);
    return response.data;
  },

  /**
   * 获取策略统计
   */
  async getPolicyStats(tenant_id?: string): Promise<ApiResponse<PolicyStats>> {
    const params = tenant_id ? { tenant_id } : undefined;
    const response = await apiClient.get('/api/v1/casbin-policies/policies/stats', { params });
    return response.data;
  },

  /**
   * 导出策略
   */
  async exportPolicies(tenant_id?: string): Promise<ApiResponse<PolicyExportResponse>> {
    const params = tenant_id ? { tenant_id } : undefined;
    const response = await apiClient.get('/api/v1/casbin-policies/policies/export', { params });
    return response.data;
  },

  /**
   * 批量创建策略
   */
  async batchCreatePolicies(request: BatchPolicyRequest): Promise<ApiResponse<string>> {
    const response = await apiClient.post('/api/v1/casbin-policies/policies/batch', request);
    return response.data;
  },

  /**
   * 清除租户所有策略
   */
  async clearTenantPolicies(tenant_id: string): Promise<ApiResponse<string>> {
    const response = await apiClient.delete(`/api/v1/casbin-policies/policies/tenant/${tenant_id}/clear`);
    return response.data;
  },

  /**
   * 同步租户策略
   */
  async syncTenantPolicies(tenant_id: string): Promise<ApiResponse<string>> {
    const response = await apiClient.post(`/api/v1/casbin-policies/policies/tenant/${tenant_id}/sync`);
    return response.data;
  },

  /**
   * 导入策略配置（文件上传）
   */
  async importPolicies(file: File, tenant_id?: string): Promise<ApiResponse<{
    imported_policies: number;
    message: string;
  }>> {
    const formData = new FormData();
    formData.append('file', file);
    if (tenant_id) {
      formData.append('tenant_id', tenant_id);
    }

    const response = await apiClient.post('/api/v1/casbin-policies/policies/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  /**
   * 根据数据范围获取角色列表
   */
  async getRolesByDataScope(dataScope: string): Promise<ApiResponse<SimpleRole[]>> {
    const response = await apiClient.get(`/api/v1/casbin-policies/data-scope/${encodeURIComponent(dataScope)}/roles`);
    return response.data;
  },

  /**
   * 获取所有数据范围类型
   */
  async getAllDataScopeTypes(): Promise<ApiResponse<string[]>> {
    const response = await apiClient.get('/api/v1/casbin-policies/data-scope/types');
    return response.data;
  },
};