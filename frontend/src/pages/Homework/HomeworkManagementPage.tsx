import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  CheckCircle2,
  FileText,
  HelpCircle,
  RefreshCw,
  Search,
  Square,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import { getTenantInfoFromLocalStorage } from "@/lib/apiUtils";
import { homeworkApi } from "@/services/homeworkApi";
import { SubjectGroupsApi } from "@/services/subjectGroupsApi";
import { CreateHomeworkDialog } from "./components/CreateHomeworkDialog";
import {
  CreateHomeworkParams,
  Homework,
  HomeworkStatistics,
  HomeworkStatusEnum,
  PageHomeworkParams,
  UpdateHomeworkParams,
} from "@/types/homework";
import { SubjectGroupsDetail } from "@/types/subjectGroups";
import EditHomework from "./components/EditHomework";
import HomeworkCardView from "./components/HomeworkCardView";
import HomeworkStatisticsCards from "./components/homeworkStatisticsCards";
import ViewToggle, { ViewMode } from "./components/ViewToggle";
import { useHomeworkStore } from "@/stores";
import SmartPagination from "./components/smartPapgination";
/**
 * 作者：朱若彪
 * 说明：作业管理页面（管理员版本）
 */
export const HomeworkManagementPage: React.FC = () => {
  const [statistics, setStatistics] = useState<HomeworkStatistics | undefined>(
    undefined
  );
  useEffect(() => {
    loadInitialData().then();
    loadSubjectGroups().then();
  }, []);
  const loadInitialData = async () => {
    const [statsDataRes] = await Promise.all([
      homeworkApi.getStatistics(tenantId, tenantName),
      loadHomeworks(),
    ]);
    if (!statsDataRes.success) {
      setError(statsDataRes.message);
      return;
    }
    setStatistics(statsDataRes.data);
  };
  // State management
  const [pageHomeworkResultList, setPageHomeworkResultList] = useState<
      Homework[]
  >([]);

  // Pagination and filtering
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<HomeworkStatusEnum | "all">(
    "all"
  );
  const [subjectGroupFilter, setSubjectGroupFilter] = useState("all");
  const [searchParams, setSearchParams] = useState<PageHomeworkParams>({
    page_params: { page: 1, page_size: 12 },
    name: undefined,
    status: undefined,
    subject_group_id: undefined,
  });


  // Dialog states
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedHomework, setSelectedHomework] = useState<Homework | null>(
    null
  );
  const [isDescriptionDialogOpen, setIsDescriptionDialogOpen] = useState(false);
  const [currentDescription, setCurrentDescription] = useState<string>("");

  // View mode state
  const [viewMode, setViewMode] = useState<ViewMode>('card');

  // Form states
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [subjectGroupNameById, setSubjectGroupNameById] = useState<
    Record<string, string>
  >({});
  // Error state
  const [error, setError] = useState<string | null>(null);

  // Create homework form
  const [homeworkForm, setHomeworkForm] = useState<CreateHomeworkParams>({
    homework_name: "",
    homework_status: HomeworkStatusEnum.Draft,
    subject_group_id: undefined,
    description: undefined,
  });

  // Get tenant ID from auth context (mock for now)
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || "";
  const tenantName = identityInfo?.schema_name || "";
  const navigate = useNavigate();
  const { reset, setHomework } = useHomeworkStore();

  const loadHomeworks = async (currParams?: PageHomeworkParams) => {
    try {
      const params: PageHomeworkParams = {
        page_params: {
          page: pagination.current,
          page_size: 12,
        },
        name: searchTerm || undefined,
        status: statusFilter === "all" ? undefined : statusFilter,
        subject_group_id:
          subjectGroupFilter === "all" ? undefined : subjectGroupFilter,
      };
      const effectiveParams: PageHomeworkParams = currParams ?? params;
      homeworkApi.pageHomework(tenantId, tenantName, effectiveParams).then((res) => {
        const { success, data, message, pagination } = res;
        if (!success) {
          return setError(message);
        }
        setPageHomeworkResultList(Array.isArray(data) ? data : []);
        setPagination({
          current: pagination?.page || 1,
          pageSize: pagination?.page_size || 12,
          total: pagination?.total || 0,
          totalPages: pagination?.total_pages || 1,
        });
      });
    } catch (err) {
      console.error("Error loading homeworks:", err);
    }
  };
  const loadSubjectGroups = async () => {
    SubjectGroupsApi.findAll(tenantName).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        return setError(message);
      }
      setSubjectGroups(Array.isArray(data) ? data : []);
      //临时代码，映射学科组id和名字，临时获取学科组name
      setSubjectGroupNameById(
        Object.fromEntries(
          (Array.isArray(data) ? data : []).map((g): [string, string] => [
            String(g.id),
            String(g.group_name ?? ""),
          ])
        )
      );
    });
  };

  const handleCreateHomework = async (formData: {
    homework_name: string;
    homework_status: HomeworkStatusEnum;
    subject_group_id?: string;
    description?: string;
  }): Promise<void> => {
    try {
      const res = await homeworkApi.createHomework(tenantId, tenantName, formData);
      const { success, message } = res;

      if (!success) {
        setError(message || "创建作业失败");
        throw new Error(message || "创建作业失败");
      }
      // 重新加载作业列表和统计信息
      await Promise.all([
        loadHomeworks(),
        loadInitialData()
      ]);
      
      return;
    } catch (err) {
      console.error("Failed to create homework", err);
      throw err;
    }
  };

  const handleUpdateHomework = async () => {
    if (!selectedHomework) return;
    try {
      await homeworkApi.updateHomework(tenantId, tenantName, {
        id: selectedHomework.id,
        homework_name: homeworkForm.homework_name,
        homework_status: homeworkForm.homework_status,
        subject_group_id: homeworkForm.subject_group_id,
        description: homeworkForm.description,
      });
      setIsEditDialogOpen(false);
      setSelectedHomework(null);
      setHomeworkForm({
        homework_name: "",
        homework_status: HomeworkStatusEnum.Draft,
        subject_group_id: undefined,
        description: "",
      });
      loadHomeworks();
    } catch (err) {
      console.error("Failed to update homework");
      console.error("Error updating homework:", err);
    }
  };

  const handleDeleteHomework = async () => {
    if (!selectedHomework) return;
    try {
      await homeworkApi.deleteHomework(
        tenantId,
        tenantName,
        selectedHomework.id.toString()
      );
      setIsDeleteDialogOpen(false);
      setSelectedHomework(null);
      loadHomeworks();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      console.error("Failed to delete homework");
      console.error("Error deleting homework:", err);
    }
  };


  const openEditDialog = (homework: Homework) => {
    setSelectedHomework(homework);
    setHomeworkForm({
      homework_name: homework.homework_name,
      homework_status: homework.homework_status as HomeworkStatusEnum,
      subject_group_id: homework.subject_group_id,
      description: homework.description || "",
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (homework: Homework) => {
    setSelectedHomework(homework);
    setIsDeleteDialogOpen(true);
  };

  // Card view event handlers
  const handleCardEdit = (homework: Homework) => {
    openEditDialog(homework);
  };

  const handleCardDelete = (homework: Homework) => {
    openDeleteDialog(homework);
  };

  const handleCardView = (homework: Homework) => {
    navigate(`/homework-setting/${homework.id}`);
  };

  const handleViewDescription = (description: string) => {
    setCurrentDescription(description);
    setIsDescriptionDialogOpen(true);
  };
  // 处理搜索
  const handleSearch = () => {
    // 重置到第一页
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    
    // 更新搜索参数
    const newSearchParams: PageHomeworkParams = {
      ...searchParams,
      page_params: {
        ...searchParams.page_params,
        page: 1 // 重置到第一页
      },
      name: searchTerm.trim() || undefined,
      status: statusFilter === "all" ? undefined : statusFilter,
      subject_group_id: subjectGroupFilter === "all" ? undefined : subjectGroupFilter,
    };
    
    setSearchParams(newSearchParams);
    loadHomeworks(newSearchParams);
  };
  
  // 添加回车键搜索功能
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };
  // 处理分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    const newPageSize = pageSize || pagination.pageSize;
    
    // 更新分页状态
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: newPageSize
    }));
    
    // 构建新的搜索参数
    const newSearchParams: PageHomeworkParams = {
      ...searchParams,
      page_params: {
        page,
        page_size: newPageSize,
      },
      name: searchTerm.trim() || undefined, 
      status: statusFilter === "all" ? undefined : statusFilter,
      subject_group_id: subjectGroupFilter === "all" ? undefined : subjectGroupFilter,
    };
    
    setSearchParams(newSearchParams);
    loadHomeworks(newSearchParams);
  }

  const getStatusBadge = (status: HomeworkStatusEnum) => {
    const statusConfig = {
      [HomeworkStatusEnum.Draft]: {
        variant: "secondary" as const,
        label: "草稿",
        icon: FileText,
      },
      [HomeworkStatusEnum.Doing]: {
        variant: "default" as const,
        label: "处理中",
        icon: CheckCircle2,
      },
      [HomeworkStatusEnum.Done]: {
        variant: "destructive" as const,
        label: "已完成",
        icon: Square,
      },
      unknown: {
        variant: "outline" as const,
        label: "未知状态",
        icon: HelpCircle,
      },
    };
    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.unknown;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };
  //------------------------------------------------------------------组件部分
  function RefreshButton() {
    return (
      <Button
        variant="outline"
        onClick={() => {
          loadInitialData();
          loadHomeworks();
        }}
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        刷新
      </Button>
    );
  }
  return (
    <div className="space-y-6 h-full flex flex-col">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">作业管理</h1>
          <p className="text-muted-foreground">创建、管理和监控作业</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton />
          <CreateHomeworkDialog
            subjectGroups={subjectGroups}
            onSubmit={handleCreateHomework}
          />
        </div>
      </div>
      {/* Statistics Cards */}
      <HomeworkStatisticsCards
        totalHomeworks={statistics?.total_homeworks || 0}
        inProgressHomeworks={statistics?.in_progress_homeworks || 0}
        completedHomeworks={statistics?.completed_homeworks || 0}
        totalStudents={statistics?.total_students || 0}
      />
      {/* Homework List */}
      <Card className="flex flex-col flex-grow">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-4">
            <CardTitle>作业列表</CardTitle>
            <ViewToggle currentView={viewMode} onViewChange={setViewMode} />
          </div>
          {/* Filters */}
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="flex gap-4 items-center">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索作业..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                className="pl-10"
              />
            </div>
            <Select
              value={statusFilter}
              onValueChange={(value) =>
                setStatusFilter(value as HomeworkStatusEnum | "all")
              }
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="draft">草稿</SelectItem>
                <SelectItem value="Doing">处理中</SelectItem>
                <SelectItem value="Done">已完成</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={subjectGroupFilter}
              onValueChange={setSubjectGroupFilter}
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部学科组</SelectItem>
                {subjectGroups.map((subjectGroup) => (
                  <SelectItem
                    key={subjectGroup.id.toString()}
                    value={subjectGroup.id.toString()}
                  >
                    {subjectGroup.group_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={handleSearch}>
              <RefreshCw className="h-4 w-4 mr-2" />
              查询
            </Button>
          </div>
        </CardHeader>
        <CardContent className="flex-grow min-h-[200px]">
          <div className="h-full">
            {viewMode === 'table' ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>作业名称</TableHead>
                    <TableHead>学科组</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pageHomeworkResultList.map((homework) => {
                    return (
                      <TableRow key={homework.id.toString()}>
                        <TableCell className="font-medium">
                          {homework.description ? (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="link"
                                  className="p-0 h-auto min-h-0 text-blue-600 font-bold"
                                  onClick={() => {
                                    reset();
                                    setHomework(homework);
                                    navigate(`/homework-setting/${homework.id}`);
                                  }}
                                >
                                  {homework.homework_name}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <span>{homework.description}</span>
                              </TooltipContent>
                            </Tooltip>
                          ) : (
                            <Button
                              variant="link"
                              className="p-0 h-auto min-h-0 text-blue-600 font-bold"
                              onClick={() => {
                                reset();
                                setHomework(homework);
                                navigate(`/homework-setting/${homework.id}`);
                              }}
                            >
                              {homework.homework_name}
                            </Button>
                          )}
                        </TableCell>
                        <TableCell>
                          {subjectGroupNameById[
                            homework.subject_group_id?.toString() || ""
                          ] || "-"}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(homework.homework_status)}
                        </TableCell>
                        <TableCell>
                          {homework.created_at
                            ? new Date(
                              homework.created_at.toString()
                            ).toLocaleString()
                            : "-"}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <HomeworkCardView
                homeworks={pageHomeworkResultList}
                subjectGroupNameById={subjectGroupNameById}
                onEdit={handleCardEdit}
                onDelete={handleCardDelete}
                onView={handleCardView}
                onViewDescription={handleViewDescription}
              />
            )}
          </div>
        </CardContent>
        {pagination && pagination.total > 0 && (
          <SmartPagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={handlePageChange}
          />
        )}
      </Card>

      {/* Edit Dialog */}
      <EditHomework
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        homeworkForm={homeworkForm as UpdateHomeworkParams}
        onHomeworkFormChange={setHomeworkForm}
        subjectGroups={subjectGroups}
        onSave={handleUpdateHomework}
        onCancel={() => setIsEditDialogOpen(false)}
      />

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              你确定要删除作业 {selectedHomework?.homework_name}{" "}
              吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteHomework}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Description Dialog */}
      <Dialog
        open={isDescriptionDialogOpen}
        onOpenChange={setIsDescriptionDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>作业描述</DialogTitle>
          </DialogHeader>
          <div style={{ whiteSpace: "pre-wrap" }}>
            {currentDescription || ""}
          </div>
          <div className="flex justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDescriptionDialogOpen(false)}
            >
              关闭
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
