import React from "react";
import {UpdateHomeworkLeafParams} from "@/types/homework";
import {SubjectGroupsDetail} from "@/types/subjectGroups";
import {Button} from '@/components/ui/button';
import {Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle} from '@/components/ui/dialog';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';

interface EditHomeworkLeafInfoProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    homeworkForm: UpdateHomeworkLeafParams;
    onHomeworkFormChange: (form: UpdateHomeworkLeafParams) => void;
    subjectGroups: SubjectGroupsDetail[];
    onSave: () => void;
    onCancel?: () => void;
}

const EditHomeworkLeafInfo: React.FC<EditHomeworkLeafInfoProps> = ({
                                                                       open,
                                                                       onOpenChange,
                                                                       homeworkForm,
                                                                       onHomeworkFormChange,
                                                                       onSave,
                                                                   }) => {
    console.log(homeworkForm)
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>编辑打印题卡信息</DialogTitle>
                    <DialogDescription>
                        修改打印题卡信息
                    </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="edit_name">作业名称</Label>
                        <Input
                            id="edit_name"
                            readOnly
                            value={homeworkForm.homework_name.toString()}
                            onChange={(e) => onHomeworkFormChange({...homeworkForm, homework_name: e.target.value})}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="homework_status">基础题卡张数 *</Label>
                        <Input
                            id="edit_name"
                            type={"number"}
                            value={homeworkForm.leaf_count}
                            onChange={(e) => onHomeworkFormChange({
                                ...homeworkForm,
                                leaf_count: parseInt(e.target.value)
                            })}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="edit_subject_group_id">基础题卡页数</Label>
                        <Input
                            id="edit_name"
                            type={"number"}
                            value={homeworkForm.page_count}
                            onChange={(e) => onHomeworkFormChange({
                                ...homeworkForm,
                                page_count: parseInt(e.target.value)
                            })}
                        />
                    </div>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        取消
                    </Button>
                    <Button onClick={onSave}>
                        保存
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
export default EditHomeworkLeafInfo;