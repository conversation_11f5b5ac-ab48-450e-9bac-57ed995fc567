import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { TeachingClassesDetail } from '@/types/teachingClasses';
import { Student } from '@/types/student';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import React, { useState, useEffect, useRef } from 'react';
import { UserPlus } from 'lucide-react';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';

interface BatchBindToHomeworkProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  classList: TeachingClassesDetail[];
  tenantId: string;
  tenantName: string;
  onBind: (studentIds: string[], classId: string) => Promise<void>;
  title?: string;
  description?: string;
}

const BatchBindToHomework: React.FC<BatchBindToHomeworkProps> = ({
  open,
  onOpenChange,
  classList,
  tenantId,
  tenantName,
  onBind,
  title = '布置作业',
  description = '请选择班级并勾选要绑定的学生',
}) => {
  const [selectedClassId, setSelectedClassId] = useState<string>('');
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isBinding, setIsBinding] = useState(false);
  const selectAllRef = useRef<HTMLButtonElement>(null);

  // 选择班级后拉取学生
  useEffect(() => {
    if (!selectedClassId) {
      setStudents([]);
      setSelectedStudents([]);
      return;
    }
    setIsLoading(true);
    TeachingClassesApi.pageStudentInClass(tenantId, tenantName, {
      class_id: selectedClassId,
      page_params:{}
    })
      .then((res) => {
        const { success, data, message } = res;
        if (!success) {
          setStudents([]);
          setSelectedStudents([]);
          console.error('报错:' + message);
        }
        setStudents(Array.isArray(data) ? data : []);
        //加载学生后自动全选
        setSelectedStudents(data.map((s) => s.id));
      })
      .finally(() => setIsLoading(false));
  }, [selectedClassId, tenantId, tenantName]);

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(students.map((s) => s.id));
    } else {
      setSelectedStudents([]);
    }
  };

  // 单个勾选
  const handleStudentToggle = (studentId: string) => {
    setSelectedStudents((prev) =>
      prev.includes(studentId)
        ? prev.filter((id) => id !== studentId)
        : [...prev, studentId]
    );
  };

  // 添加
  const handleBind = async () => {
    if (selectedStudents.length === 0) return;
    setIsBinding(true);
    try {
      await onBind(selectedStudents, selectedClassId);
      setSelectedStudents([]);
      setStudents([]);
      setSelectedClassId('');
      onOpenChange(false);
    } catch (error) {
      // 错误处理可自定义
      console.error('绑定学生失败:', error);
    } finally {
      setIsBinding(false);
    }
  };

  // 取消
  const handleCancel = () => {
    setSelectedStudents([]);
    setStudents([]);
    setSelectedClassId('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {/* 班级选择 */}
          <div>
            <Label htmlFor="class_select">选择班级</Label>
            <Select
              value={selectedClassId}
              onValueChange={(value) => setSelectedClassId(value)}
              disabled={isLoading || isBinding}
            >
              <SelectTrigger>
                <SelectValue placeholder="请选择班级" />
              </SelectTrigger>
              <SelectContent>
                {classList.map((cls) => (
                  <SelectItem key={cls.id as string} value={cls.id as string}>
                    {cls.class_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 学生列表 */}
          {selectedClassId && (
            <div className="space-y-2">
              <Label>学生列表 {isLoading ? '(加载中...)' : `(${students.length}人)`}</Label>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead  className="w-12">
                        <Checkbox
                          ref={selectAllRef}
                          checked={students.length > 0 && selectedStudents.length === students.length}
                          onCheckedChange={(checked) => handleSelectAll(!!checked)}
                          disabled={students.length === 0}
                        />
                      </TableHead>
                      <TableHead className="w-32">姓名</TableHead>
                      <TableHead className="w-32">学号</TableHead>
                      <TableHead className="w-20">性别</TableHead>
                    </TableRow>
                  </TableHeader>
                </Table>
                <ScrollArea className="h-[300px]  overflow-x-auto">
                  <Table className="min-w-full">
                    <TableBody>
                      {students.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell className="w-12">
                            <Checkbox
                              checked={selectedStudents.includes(student.id)}
                              onCheckedChange={() => handleStudentToggle(student.id)}
                            />
                          </TableCell>
                          <TableCell className="w-32">{student.student_name}</TableCell>
                          <TableCell className="w-32">{student.student_number}</TableCell>
                          <TableCell className="w-20">{student.gender || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
            </div>
            </div>
          )}
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={isBinding}>
            取消
          </Button>
          <Button
            onClick={handleBind}
            disabled={selectedStudents.length === 0 || isBinding}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            {isBinding ? '添加中...' : `确定 (${selectedStudents.length})`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BatchBindToHomework;
