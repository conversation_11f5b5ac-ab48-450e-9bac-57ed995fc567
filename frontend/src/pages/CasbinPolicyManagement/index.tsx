import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious, PaginationEllipsis } from '@/components/ui/pagination';
import {
  Plus, 
} from 'lucide-react';

import { usePolicyManagement } from './hooks/usePolicyManagement';
import PolicyStats from './components/PolicyStats';
import PolicyFilters from './components/PolicyFilters';
import PolicyTable from './components/PolicyTable';
import CreatePolicyDialog from './components/CreatePolicyDialog';
import { CasbinPolicy } from './types/policy';

// 分页辅助函数
const generatePagination = (currentPage: number, totalPages: number): (number | string)[] => {
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 4) {
    return [1, 2, 3, 4, 5, '...', totalPages];
  }

  if (currentPage >= totalPages - 3) {
    return [1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  return [
    1,
    '...',
    currentPage - 1,
    currentPage,
    currentPage + 1,
    '...',
    totalPages,
  ];
};

const CasbinPolicyManagementPage: React.FC = () => {
  const {
    policies,
    stats,
    loading,
    error,
    currentPage,
    totalPages,
    filters,
    createPolicy,
    deletePolicy,
    updateFilters,
    setCurrentPage,
  } = usePolicyManagement();

  // 对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<CasbinPolicy | null>(null);

  // 处理删除策略
  const handleDeletePolicy = async () => {
    if (!selectedPolicy) return;
    
    const success = await deletePolicy(selectedPolicy);
    if (success) {
      setIsDeleteDialogOpen(false);
      setSelectedPolicy(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页头 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Casbin策略管理</h1>
          <p className="text-slate-600 mt-2">
            管理Casbin权限策略和角色关系，支持策略测试和批量操作
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            新建策略
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <PolicyStats stats={stats} loading={loading} />

      {/* 主要内容 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">策略列表</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 筛选栏 */}
          <PolicyFilters 
            filters={filters}
            onFiltersChange={updateFilters}
            onRefresh={() => window.location.reload()}
          />

          {/* 错误提示 */}
          {error && (
            <div className="text-center py-4 text-red-500 bg-red-50 rounded-md">
              {error}
            </div>
          )}

          {/* 策略表格 */}
          <PolicyTable 
            policies={policies}
            loading={loading}
            onDelete={(policy) => {
              setSelectedPolicy(policy);
              setIsDeleteDialogOpen(true);
            }}
          />

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationPrevious
                    onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                    className={currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                  {generatePagination(currentPage, totalPages).map((page, index) => (
                    <PaginationItem key={index}>
                      {typeof page === 'string' ? (
                        <PaginationEllipsis />
                      ) : (
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ))}
                  <PaginationNext
                    onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
                    className={currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建策略对话框 */}
      <CreatePolicyDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={createPolicy}
        tenantId={filters.tenantId}
      />

      {/* 删除策略确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条策略吗？此操作无法撤销。
              <br />
              策略详情: {selectedPolicy?.subject} → {selectedPolicy?.object} | {selectedPolicy?.action}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeletePolicy}>
              删除
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default CasbinPolicyManagementPage; 