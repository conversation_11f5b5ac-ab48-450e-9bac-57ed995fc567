import React, {useEffect, useMemo, useState} from "react";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {CheckCircle2, LibraryBig} from "lucide-react";
import {useNavigate} from "react-router-dom";
import {MetricCard} from "@/pages/Workspace/components/MetricsCards.tsx";
import {AssembleCardMetrics, homeworkAnalysisApi} from "@/services/analysisApi"
import {getTenantInfoFromLocalStorage} from "@/lib/apiUtils.ts";

const ConsumeQuestionCards: React.FC = () => {
    const navigate = useNavigate();
    const identityInfo = getTenantInfoFromLocalStorage();
    const schema_name = identityInfo?.schema_name || '';
    const [data, setData] = useState<AssembleCardMetrics | null>(null);

    useEffect(() => {
        if (!schema_name) return;
        loadInitialData();
    }, [schema_name]);

    const loadInitialData = async () => {
        try {
            const res = await homeworkAnalysisApi.getAssembleCardMetrics(schema_name)
            const {success, data} = res;
            console.log('getAssembleCardMetrics', res)
            if (success) {
                setData(data ?? null);
            }
        } catch (error) {
            console.error('获取题卡数据失败:', error);
        }
    };
    const metrics = useMemo(() => {
        if (!data) return [];

        return [
            {
                title: '扫描数量(张/页)',
                value: `${data.scanned_leaf_total}/${data.scanned_page_total}`,
                description: '',
                icon: <CheckCircle2 className="h-4 w-4"/>,
                status: 'normal' as const,
                onClick: () => navigate('/subject-groups')
            },
            {
                title: '打印试卷数量(张/页)',
                value: `${data.leaf_total}/${data.page_total}`,
                description: '',
                icon: <CheckCircle2 className="h-4 w-4"/>,
                status: 'normal' as const,
                onClick: () => navigate('/subject-groups')
            },
            {
                title: '作业次数',
                value: `${data.homework_total}`,
                description: '',
                icon: <CheckCircle2 className="h-4 w-4"/>,
                status: 'normal' as const,
                onClick: () => navigate('/subject-groups')
            }
        ];
    }, [data]);
    return (
        <Card className="h-full flex flex-col">
            <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                    <LibraryBig className="h-5 w-5"/>
                    <span>题卡数据看板</span>
                </CardTitle>
                <CardDescription>显示题卡使用数据</CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
                    {metrics.map((metric, index) => (
                        <MetricCard key={index} {...metric} />
                    ))}
                </div>
            </CardContent>
        </Card>
    )
}

export default ConsumeQuestionCards