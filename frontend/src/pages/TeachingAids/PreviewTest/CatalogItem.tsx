import React from "react";
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from "@/components/ui/collapsible.tsx";
import {cn} from "@/lib/utils.ts";
import {Button} from "@/components/ui/button.tsx";
import {ChevronDown, ChevronRight} from "lucide-react";
import {CatalogTreeNode} from "@/types/teachingAid.ts";

interface CatalogItemProps{
    chapter: CatalogTreeNode;
    selectedChapter: CatalogTreeNode | null;
    onSelectChapter: (chapter: CatalogTreeNode) => void;
    level: number;
}

const CatalogItem: React.FC<CatalogItemProps> = ({ chapter, selectedChapter, onSelectChapter, level }) => {
    const hasChildren = chapter.children && chapter.children.length > 0;
    const [isOpen, setIsOpen] = React.useState(true); // Automatically open the first level

    return (
        <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
            <div className={cn("flex items-center space-x-1", `pl-${level * 2}`)}>
                {hasChildren && (
                    <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                            {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                        </Button>
                    </CollapsibleTrigger>
                )}
                <Button
                    variant={selectedChapter?.id === chapter.id ? 'secondary' : 'ghost'}
                    className={cn(
                        'w-full justify-start text-left h-auto py-2 px-3',
                        selectedChapter?.id === chapter.id && 'font-bold',
                        !hasChildren && 'ml-9' // Add margin if no children to align with parent
                    )}
                    onClick={() => onSelectChapter(chapter)}
                >
                    <div>
                        <div className='text-base'>{chapter.title}</div>
                    </div>
                </Button>
            </div>
            {hasChildren && (
                <CollapsibleContent className="py-1">
                    {chapter.children?.map((child) => (
                        <CatalogItem
                            key={child.id}
                            chapter={child}
                            selectedChapter={selectedChapter}
                            onSelectChapter={onSelectChapter}
                            level={level + 1}
                        />
                    ))}
                </CollapsibleContent>
            )}
        </Collapsible>
    );
};


export default CatalogItem;
