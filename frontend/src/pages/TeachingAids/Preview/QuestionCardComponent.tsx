import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Dialog, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Node } from './tiptap-types';

interface QuestionCardProps {
  node: Node;
}

const QuestionCardComponent: React.FC<QuestionCardProps> = ({ node }) => {
  // The node.attrs (e.g., paperTitle, id) are encapsulated here,
  // ready for future functionalities like fetching dynamic paper content.
  const navigate = useNavigate();


  return (
    <div className="flex justify-center my-4">
      <Dialog>
        <DialogHeader>
          <DialogTitle></DialogTitle>
        </DialogHeader>
        <Card className="w-full max-w-lg">
          <CardHeader>
            <CardTitle>答题卡</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6 pt-0">
            <p className="text-center text-lg font-semibold">{node.attrs?.answerSheetTitle}</p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <DialogTrigger asChild>
              <Button onClick={() => node.attrs?.answerSheetId && navigate(`/answerSheetEditing/public/${node.attrs.answerSheetId}`)}>点击预览</Button>
            </DialogTrigger>
          </CardFooter>
        </Card>
      </Dialog>
    </div>
  );
};

export default QuestionCardComponent;
