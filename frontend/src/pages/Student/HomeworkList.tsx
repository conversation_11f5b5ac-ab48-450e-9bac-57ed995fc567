import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { CalendarClock, Search } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import CustomPagination from '@/components/Pagination';
import { useNavigate } from 'react-router-dom';
import { studentScoreApi, HomeworkListResponse } from '@/services/studentScoreApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { SubjectGroupsDetail } from '@/types/subjectGroups.ts';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { toast } from 'sonner';
import { HomeworkStatusEnum } from '@/types/homework';

const HomeworkList: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [homeworks, setHomeworks] = useState<HomeworkListResponse[]>([]);
  const [searchParams, setSearchParams] = useState({
    name: '',
  });
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(20);
  const [total, setTotal] = useState(10);
  const navigate = useNavigate();
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantName = identityInfo?.schema_name || '';
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState<HomeworkStatusEnum | 'all'>('all');
  const [subjectGroupFilter, setSubjectGroupFilter] = useState('all');

  useEffect(() => {
    fetchHomeworks();
    loadSubjectGroups();
  }, [page, size]);

  const fetchHomeworks = async () => {
    try {
      const response = await studentScoreApi.getStudentHomeworks(tenantName, {
        name: searchParams.name || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
        subject_group_id: subjectGroupFilter === 'all' ? undefined : subjectGroupFilter,
        page_params: {
          page: page,
          page_size: size,
        },
      });
      setHomeworks(response.data || []);
      setTotal(response.pagination.total || 0);
    } catch (error) {
      setError('获取作业列表失败');
    }
  };

  const loadSubjectGroups = async () => {
    try {
      setLoading(true);
      const response = await SubjectGroupsApi.findAll(tenantName);
      if (response.success && response.data) {
        setSubjectGroups(response.data);
      }
    } catch (error) {
      console.error('Failed to load subject groups:', error);
      toast.error('加载学科组列表失败');
    } finally {
      setLoading(false);
    }
  };

  const getSubjectGroupName = (subjectGroupId: string) => {
    const subjectGroup = subjectGroups.find((group) => group.id === subjectGroupId);
    return subjectGroup?.group_name || '未知';
  };

  const paginationChange = async (page: number, pageSize: number) => {
    await new Promise((resolve) => {
      setPage(page);
      setSize(pageSize);
      setTimeout(resolve, 0);
    });
    fetchHomeworks();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div>
      <Card className="flex flex-col flex-grow">
        <CardHeader className="flex flex-row items-center justify-between">
          {error && <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">{error}</div>}

          <h2 className="text-2xl font-bold">作业列表</h2>
          <div className="flex gap-4 items-center">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input placeholder="请输入作业名称进行搜索" value={searchParams.name} onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })} className="pl-10" />
            </div>
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as HomeworkStatusEnum | 'all')}>
              <SelectTrigger className="w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="draft">草稿</SelectItem>
                <SelectItem value="Doing">处理中</SelectItem>
                <SelectItem value="Done">已完成</SelectItem>
              </SelectContent>
            </Select>
            <Select value={subjectGroupFilter} onValueChange={setSubjectGroupFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部学科组</SelectItem>
                {subjectGroups.map((subjectGroup) => (
                  <SelectItem key={subjectGroup.id.toString()} value={subjectGroup.id.toString()}>
                    {subjectGroup.group_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={fetchHomeworks}>
              <Search className="h-4 w-4 mr-2" />
              查询
            </Button>
          </div>
        </CardHeader>
        <CardContent className="flex-grow min-h-[200px]">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {homeworks.map((homework, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4 cursor-pointer"
                style={{
                  borderLeftColor: '#3b82f6',
                }}
                onClick={() => navigate(`/student-score/report`, { state: { homework: homework } })}
              >
                <CardHeader className="p-4 relative z-10">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-base font-semibold leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors cursor-pointer">{homework.homework_name}</CardTitle>
                      <div className="flex items-center gap-2 mt-2 flex-wrap justify-between">
                        <Badge variant="outline" className="text-xs text-muted-foreground">
                          {getSubjectGroupName(homework.subject_group_id)}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <CalendarClock className="h-4 w-4" />
                          <span className="text-sm text-muted-foreground">{new Date(homework.created_at).toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
          <div className="flex justify-center">
            <CustomPagination
              className="pt-2"
              total={total}
              current={page}
              pageSize={size}
              onChange={(page, size) => {
                paginationChange(page, size);
              }}
              showSizeChanger={true}
              showTotal={true}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomeworkList;
